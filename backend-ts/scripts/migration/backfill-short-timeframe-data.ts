import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';


/**
 * 数据回填脚本
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

const prisma = new PrismaClient();

/**
 * 补充短时间框架历史数据
 */
class ShortTimeframeBackfiller {
  private readonly BINANCE_API_BASE = 'https://api.binance.com/api/v3';
  private readonly RATE_LIMIT_DELAY = 100; // 毫秒
  private readonly BATCH_SIZE = 1000; // 每次请求的数据量

  async backfillShortTimeframeData() {
    console.log('🔄 开始补充短时间框架历史数据...\n');

    try {
      await prisma.$connect();
      console.log('✅ 数据库连接成功\n');

      const btcSymbol = await prisma.symbol.findFirst({
        where: { symbol: 'BTC/USDT' }
      });

      if (!btcSymbol) {
        throw new Error('未找到BTC/USDT符号');
      }

      // 1. 补充5分钟数据（优先最近2年）
      await this.backfillTimeframe(btcSymbol.id, '5m', 730); // 最近2年

      // 2. 补充15分钟数据（优先最近3年）
      await this.backfillTimeframe(btcSymbol.id, '15m', 1095); // 最近3年

      console.log('\n🎉 短时间框架历史数据补充完成！');

    } catch (error) {
      console.error('❌ 数据补充失败:', error);
      throw error;
    } finally {
      await prisma.$disconnect();
    }
  }

  private async backfillTimeframe(symbolId: string, timeframe: string, maxDays: number) {
    console.log(`📈 开始补充 ${timeframe} 数据 (最近${maxDays}天):`);
    console.log('─'.repeat(60));

    // 获取当前最早的数据
    const earliestData = await prisma.historicalData.findFirst({
      where: { symbolId, timeframe },
      orderBy: { timestamp: 'asc' }
    });

    // 计算需要补充的时间范围
    const endTime = earliestData ? earliestData.timestamp : new Date();
    const startTime = new Date(endTime.getTime() - maxDays * 24 * 60 * 60 * 1000);
    
    // 确保不早于Binance开始时间
    const binanceStartTime = new Date('2017-08-17T00:00:00Z');
    const actualStartTime = startTime < binanceStartTime ? binanceStartTime : startTime;

    console.log(`📅 补充时间范围: ${actualStartTime.toISOString()} ~ ${endTime.toISOString()}`);

    if (actualStartTime >= endTime) {
      console.log('✅ 该时间框架数据已完整，无需补充\n');
      return;
    }

    // 分批获取数据
    let currentTime = actualStartTime;
    let totalAdded = 0;
    let batchCount = 0;

    while (currentTime < endTime) {
      try {
        batchCount++;
        console.log(`🔄 处理批次 ${batchCount}: ${currentTime.toISOString()}`);

        // 计算批次结束时间
        const intervalMs = this.getIntervalMs(timeframe);
        const batchEndTime = new Date(Math.min(
          currentTime.getTime() + this.BATCH_SIZE * intervalMs,
          endTime.getTime()
        ));

        // 获取数据
        const klineData = await this.fetchKlineData('BTCUSDT', timeframe, currentTime, batchEndTime);
        
        if (klineData.length === 0) {
          console.log(`   ⚠️ 未获取到数据，跳过`);
          currentTime = batchEndTime;
          continue;
        }

        // 过滤已存在的数据
        const newData = await this.filterExistingData(symbolId, timeframe, klineData);
        
        if (newData.length === 0) {
          console.log(`   ✅ 数据已存在，跳过`);
          currentTime = batchEndTime;
          continue;
        }

        // 保存数据
        const savedCount = await this.saveHistoricalData(symbolId, timeframe, newData);
        totalAdded += savedCount;

        console.log(`   ✅ 新增 ${savedCount} 条数据`);

        // 更新当前时间
        currentTime = batchEndTime;

        // 速率限制
        await this.delay(this.RATE_LIMIT_DELAY);

        // 每10个批次显示进度
        if (batchCount % 10 === 0) {
          const progress = ((currentTime.getTime() - actualStartTime.getTime()) / 
                          (endTime.getTime() - actualStartTime.getTime()) * 100).toFixed(1);
          console.log(`   📊 进度: ${progress}% (已新增 ${totalAdded} 条)`);
        }

      } catch (error) {
        console.error(`   ❌ 批次处理失败: ${error.message}`);
        
        // 如果是API限制错误，等待更长时间
        if (error.message.includes('429') || error.message.includes('rate limit')) {
          console.log('   ⏰ 遇到API限制，等待60秒...');
          await this.delay(60000);
        } else {
          // 其他错误，跳过这个批次
          currentTime = new Date(currentTime.getTime() + this.BATCH_SIZE * this.getIntervalMs(timeframe));
        }
      }
    }

    console.log(`✅ ${timeframe} 数据补充完成，总计新增 ${totalAdded} 条数据\n`);
  }

  private async fetchKlineData(symbol: string, interval: string, startTime: Date, endTime: Date): Promise<any[]> {
    const url = `${this.BINANCE_API_BASE}/klines`;
    const params = new URLSearchParams({
      symbol,
      interval,
      startTime: startTime.getTime().toString(),
      endTime: endTime.getTime().toString(),
      limit: this.BATCH_SIZE.toString()
    });

    const response = await fetch(`${url}?${params}`);
    
    if (!response.ok) {
      throw new Error(`Binance API错误: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!Array.isArray(data)) {
      throw new Error('Binance API返回数据格式错误');
    }

    return data;
  }

  private async filterExistingData(symbolId: string, timeframe: string, klineData: any[]): Promise<any[]> {
    if (klineData.length === 0) return [];

    // 获取时间戳范围
    const timestamps = klineData.map(k => new Date(k[0]));
    const minTime = new Date(Math.min(...timestamps.map(t => t.getTime())));
    const maxTime = new Date(Math.max(...timestamps.map(t => t.getTime())));

    // 查询已存在的数据
    const existingData = await prisma.historicalData.findMany({
      where: {
        symbolId,
        timeframe,
        timestamp: {
          gte: minTime,
          lte: maxTime
        }
      },
      select: { timestamp: true }
    });

    const existingTimestamps = new Set(
      existingData.map(d => d.timestamp.getTime())
    );

    // 过滤掉已存在的数据
    return klineData.filter(k => !existingTimestamps.has(k[0]));
  }

  private async saveHistoricalData(symbolId: string, timeframe: string, klineData: any[]): Promise<number> {
    if (klineData.length === 0) return 0;

    const historicalDataRecords = klineData.map(kline => ({
      id: crypto.randomUUID(),
      symbolId,
      timeframe,
      timestamp: new Date(kline[0]),
      openPrice: parseFloat(kline[1]),
      highPrice: parseFloat(kline[2]),
      lowPrice: parseFloat(kline[3]),
      closePrice: parseFloat(kline[4]),
      volume: parseFloat(kline[5]),
      trades: kline[8] || 0,
      quoteVolume: parseFloat(kline[7]),
      dataQuality: 1.0,
      isProtected: true,
      dataSource: 'binance',
      createdAt: new Date(),
      updatedAt: new Date()
    }));

    try {
      await prisma.historicalData.createMany({
        data: historicalDataRecords,
        skipDuplicates: true
      });

      return historicalDataRecords.length;
    } catch (error) {
      console.error('保存数据失败:', error);
      return 0;
    }
  }

  private getIntervalMs(timeframe: string): number {
    const intervals = {
      '1m': 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '8h': 8 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '3d': 3 * 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000
    };
    return intervals[timeframe] || 60 * 60 * 1000;
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行数据补充
async function main() {
  const backfiller = new ShortTimeframeBackfiller();
  
  try {
    await backfiller.backfillShortTimeframeData();
    
    console.log('\n💡 补充完成后建议:');
    console.log('   • 运行数据完整性检查验证新数据');
    console.log('   • 重新生成技术指标以利用更多历史数据');
    console.log('   • 测试短期交易策略的准确性提升');
    console.log('   • 监控数据库性能和存储使用情况');
    
  } catch (error) {
    console.error('❌ 数据补充失败:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}
