/**
 * 用户数据迁移脚本
 * 将现有的基于浏览器指纹的用户配置迁移到新的认证系统
 */

import { PrismaClient } from '@prisma/client';
import { createHash } from 'crypto';


/**
 * 用户数据迁移
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

const prisma = new PrismaClient();

interface MigrationStats {
  totalUserConfigs: number;
  totalModelPreferences: number;
  uniqueUserIds: number;
  migratedUsers: number;
  errors: string[];
}

/**
 * 生成用户名（基于用户ID哈希）
 */
function generateUsername(userId: string): string {
  const hash = createHash('sha256').update(userId).digest('hex');
  return `user_${hash.substring(0, 8)}`;
}

/**
 * 生成临时邮箱（基于用户ID）
 */
function generateTempEmail(userId: string): string {
  const hash = createHash('sha256').update(userId).digest('hex');
  return `temp_${hash.substring(0, 12)}@crypto-monitor.local`;
}

/**
 * 生成临时密码哈希
 */
function generateTempPasswordHash(): string {
  // 生成一个随机的临时密码哈希
  // 实际使用时需要用户重新设置密码
  const tempPassword = Math.random().toString(36).substring(2, 15);
  return createHash('sha256').update(tempPassword).digest('hex');
}

/**
 * 获取所有唯一的用户ID
 */
async function getUniqueUserIds(): Promise<string[]> {
  const [configUserIds, preferenceUserIds] = await Promise.all([
    prisma.userLLMConfig.findMany({
      select: { userId: true },
      distinct: ['userId']
    }),
    prisma.userModelPreferences.findMany({
      select: { userId: true },
      distinct: ['userId']
    })
  ]);

  const allUserIds = new Set([
    ...configUserIds.map(c => c.userId),
    ...preferenceUserIds.map(p => p.userId)
  ]);

  return Array.from(allUserIds);
}

/**
 * 创建用户记录
 */
async function createUserFromLegacyId(legacyUserId: string): Promise<string> {
  const username = generateUsername(legacyUserId);
  const email = generateTempEmail(legacyUserId);
  const passwordHash = generateTempPasswordHash();

  const user = await prisma.users.create({
    data: {
      username,
      email,
      passwordHash,
      role: 'USER',
      isActive: true,
      isEmailVerified: false,
    }
  });

  console.log(`✅ 创建用户: ${username} (${user.id}) <- ${legacyUserId}`);
  return user.id;
}

/**
 * 执行数据迁移
 */
async function migrateUserData(): Promise<MigrationStats> {
  const stats: MigrationStats = {
    totalUserConfigs: 0,
    totalModelPreferences: 0,
    uniqueUserIds: 0,
    migratedUsers: 0,
    errors: []
  };

  try {
    console.log('🚀 开始用户数据迁移...\n');

    // 1. 统计现有数据
    const [configCount, preferenceCount] = await Promise.all([
      prisma.userLLMConfig.count(),
      prisma.userModelPreferences.count()
    ]);

    stats.totalUserConfigs = configCount;
    stats.totalModelPreferences = preferenceCount;

    console.log(`📊 现有数据统计:`);
    console.log(`   - 用户LLM配置: ${configCount} 条`);
    console.log(`   - 用户模型偏好: ${preferenceCount} 条`);

    // 2. 获取所有唯一用户ID
    const uniqueUserIds = await getUniqueUserIds();
    stats.uniqueUserIds = uniqueUserIds.length;

    console.log(`   - 唯一用户ID: ${uniqueUserIds.length} 个\n`);

    if (uniqueUserIds.length === 0) {
      console.log('✅ 没有需要迁移的用户数据');
      return stats;
    }

    // 3. 为每个唯一用户ID创建用户记录
    console.log('👥 开始创建用户记录...');
    
    for (const legacyUserId of uniqueUserIds) {
      try {
        await createUserFromLegacyId(legacyUserId);
        stats.migratedUsers++;
      } catch (error) {
        const errorMsg = `创建用户失败 ${legacyUserId}: ${error instanceof Error ? error.message : String(error)}`;
        console.error(`❌ ${errorMsg}`);
        stats.errors.push(errorMsg);
      }
    }

    console.log(`\n✅ 迁移完成!`);
    console.log(`📈 迁移统计:`);
    console.log(`   - 成功创建用户: ${stats.migratedUsers} 个`);
    console.log(`   - 失败: ${stats.errors.length} 个`);

    if (stats.errors.length > 0) {
      console.log(`\n❌ 错误详情:`);
      stats.errors.forEach(error => console.log(`   - ${error}`));
    }

    console.log(`\n📝 后续步骤:`);
    console.log(`   1. 用户需要通过邀请码重新注册或重置密码`);
    console.log(`   2. 现有的用户配置数据保持不变，通过userId关联`);
    console.log(`   3. 新的认证系统将使用Users表进行用户管理`);

  } catch (error) {
    const errorMsg = `迁移过程中发生错误: ${error instanceof Error ? error.message : String(error)}`;
    console.error(`❌ ${errorMsg}`);
    stats.errors.push(errorMsg);
  }

  return stats;
}

/**
 * 回滚迁移（删除所有创建的用户）
 */
async function rollbackMigration(): Promise<void> {
  console.log('🔄 开始回滚迁移...');
  
  try {
    const deletedUsers = await prisma.users.deleteMany({
      where: {
        email: {
          contains: '@crypto-monitor.local'
        }
      }
    });

    console.log(`✅ 回滚完成，删除了 ${deletedUsers.count} 个临时用户`);
  } catch (error) {
    console.error(`❌ 回滚失败: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * 主函数
 */
async function main() {
  const command = process.argv[2];

  try {
    switch (command) {
      case 'migrate':
        await migrateUserData();
        break;
      case 'rollback':
        await rollbackMigration();
        break;
      case 'stats':
        const stats = await migrateUserData();
        console.log('\n📊 迁移统计:', JSON.stringify(stats, null, 2));
        break;
      default:
        console.log('用法:');
        console.log('  npm run migrate-users migrate   # 执行迁移');
        console.log('  npm run migrate-users rollback  # 回滚迁移');
        console.log('  npm run migrate-users stats     # 查看统计');
        break;
    }
  } catch (error) {
    console.error('❌ 执行失败:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

export { migrateUserData, rollbackMigration };
