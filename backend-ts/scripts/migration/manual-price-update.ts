import { PrismaClient } from '@prisma/client';
import { config } from 'dotenv';


/**
 * 手动价格更新
 * 注意：此脚本使用独立的PrismaClient实例，因为它是独立运行的脚本
 * 不是应用程序的一部分，不需要使用全局单例
 */

config();

const prisma = new PrismaClient();

/**
 * 手动更新价格数据
 */
async function manualPriceUpdate() {
  console.log('💰 手动更新价格数据...\n');

  try {
    await prisma.$connect();

    // 获取BTC符号记录
    const symbolRecord = await prisma.symbols.findUnique({
      where: { symbol: 'BTC/USDT' }
    });

    if (!symbolRecord) {
      throw new Error('BTC/USDT符号记录不存在');
    }

    console.log('✅ 找到符号记录:', symbolRecord.symbol);

    // 获取最新的15分钟数据作为当前价格
    const latestData = await prisma.historicalData.findFirst({
      where: { symbolId: symbolRecord.id, timeframe: '15m' },
      orderBy: { timestamp: 'desc' }
    });

    if (!latestData) {
      throw new Error('没有找到15分钟历史数据');
    }

    console.log('✅ 最新15分钟数据:', {
      timestamp: latestData.timestamp,
      closePrice: latestData.closePrice.toString(),
      timeframe: latestData.timeframe
    });

    const currentPrice = latestData.closePrice;

    // 获取24小时前的数据来计算变化
    const yesterday = new Date(latestData.timestamp.getTime() - 24 * 60 * 60 * 1000);
    const yesterdayData = await prisma.historicalData.findFirst({
      where: {
        symbolId: symbolRecord.id,
        timeframe: '15m',
        timestamp: { lte: yesterday }
      },
      orderBy: { timestamp: 'desc' }
    });

    const yesterdayPrice = yesterdayData ? yesterdayData.closePrice : currentPrice;
    const change24h = currentPrice.toNumber() - yesterdayPrice.toNumber();
    const changePercent24h = ((change24h / yesterdayPrice.toNumber()) * 100);

    console.log('📊 价格变化计算:', {
      currentPrice: currentPrice.toString(),
      yesterdayPrice: yesterdayPrice.toString(),
      change24h: change24h.toFixed(2),
      changePercent24h: changePercent24h.toFixed(2) + '%'
    });

    // 获取24小时内的最高价和最低价
    const last24hData = await prisma.historicalData.findMany({
      where: {
        symbolId: symbolRecord.id,
        timeframe: '15m',
        timestamp: { gte: yesterday }
      },
      select: { highPrice: true, lowPrice: true, volume: true }
    });

    const high24h = Math.max(...last24hData.map(d => d.highPrice.toNumber()));
    const low24h = Math.min(...last24hData.map(d => d.lowPrice.toNumber()));
    const volume24h = last24hData.reduce((sum, d) => sum + d.volume.toNumber(), 0);

    console.log('📈 24小时统计:', {
      high24h: high24h.toFixed(2),
      low24h: low24h.toFixed(2),
      volume24h: volume24h.toFixed(2)
    });

    // 删除旧的价格数据
    const deleteResult = await prisma.priceData.deleteMany({
      where: { symbolId: symbolRecord.id }
    });

    console.log('🗑️ 删除旧价格数据:', deleteResult.count, '条');

    // 创建新的价格数据
    const newPriceData = await prisma.priceData.create({
      data: {
        symbolId: symbolRecord.id,
        price: currentPrice,
        change24h,
        changePercent24h,
        volume24h,
        high24h,
        low24h,
        timestamp: latestData.timestamp,
        createdAt: new Date()
      }
    });

    console.log('✅ 价格数据更新成功:', {
      id: newPriceData.id,
      price: newPriceData.price.toString(),
      timestamp: newPriceData.timestamp,
      change24h: newPriceData.change24h.toFixed(2),
      changePercent24h: newPriceData.changePercent24h.toFixed(2) + '%'
    });

    // 验证更新结果
    const updatedPriceData = await prisma.priceData.findFirst({
      where: { symbolId: symbolRecord.id },
      orderBy: { timestamp: 'desc' },
      include: { symbols: { select: { symbol: true } } }
    });

    if (updatedPriceData) {
      const hoursAgo = (Date.now() - updatedPriceData.timestamp.getTime()) / (1000 * 60 * 60);
      console.log('\n🎉 验证结果:');
      console.log(`   符号: ${updatedPriceData.symbols.symbol}`);
      console.log(`   价格: $${updatedPriceData.price.toString()}`);
      console.log(`   时间: ${hoursAgo.toFixed(1)} 小时前`);
      console.log(`   状态: ${hoursAgo < 1 ? '✅ 新鲜' : '⚠️ 过时'}`);
    }

  } catch (error) {
    console.error('❌ 价格数据更新失败:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行更新
if (require.main === module) {
  manualPriceUpdate().catch(console.error);
}
