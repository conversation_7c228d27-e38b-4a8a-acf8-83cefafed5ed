#!/usr/bin/env ts-node

import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

interface TestQualityIssue {
  type: 'coverage' | 'strategy' | 'mock_quality' | 'e2e_completeness';
  severity: 'high' | 'medium' | 'low';
  file: string;
  line?: number;
  description: string;
  suggestion: string;
}

interface TestCoverageStats {
  totalFiles: number;
  testedFiles: number;
  coveragePercentage: number;
  missingTests: string[];
}

interface TestStrategyAnalysis {
  testLocations: string[];
  inconsistentPatterns: string[];
  duplicatedTestLogic: string[];
}

class TestQualityDetector {
  private issues: TestQualityIssue[] = [];
  private srcDir = 'src';
  private testDirs = ['tests', 'src/tests', 'src/**/__tests__', 'src/**/*.test.ts', 'src/**/*.spec.ts'];

  async detectIssues(): Promise<TestQualityIssue[]> {
    console.log('🧪 开始测试质量检测...');
    
    await this.analyzeCoverage();
    await this.analyzeTestStrategy();
    await this.analyzeMockQuality();
    await this.analyzeE2ECompleteness();
    
    return this.issues;
  }

  private async analyzeCoverage(): Promise<void> {
    console.log('📊 分析测试覆盖率...');
    
    const sourceFiles = await glob(`${this.srcDir}/**/*.ts`, {
      ignore: ['**/*.test.ts', '**/*.spec.ts', '**/__tests__/**', '**/tests/**']
    });
    
    const testFiles = await glob('**/*.{test,spec}.ts');
    
    const stats = this.calculateCoverage(sourceFiles, testFiles);
    
    if (stats.coveragePercentage < 60) {
      this.issues.push({
        type: 'coverage',
        severity: 'high',
        file: 'project-wide',
        description: `测试覆盖率过低: ${stats.coveragePercentage.toFixed(1)}% (${stats.testedFiles}/${stats.totalFiles} 文件)`,
        suggestion: '增加单元测试，目标覆盖率应达到80%以上'
      });
    }
    
    // 检查关键业务文件是否有测试
    const criticalFiles = sourceFiles.filter(file => 
      file.includes('service') || 
      file.includes('engine') || 
      file.includes('calculator') || 
      file.includes('analyzer')
    );
    
    for (const file of criticalFiles) {
      const hasTest = this.hasCorrespondingTest(file, testFiles);
      if (!hasTest) {
        this.issues.push({
          type: 'coverage',
          severity: 'high',
          file,
          description: '关键业务文件缺少测试',
          suggestion: '为核心业务逻辑添加单元测试'
        });
      }
    }
  }

  private async analyzeTestStrategy(): Promise<void> {
    console.log('🎯 分析测试策略一致性...');
    
    const testFiles = await glob('**/*.{test,spec}.ts');
    const testLocations = new Set<string>();
    
    testFiles.forEach(file => {
      const dir = path.dirname(file);
      testLocations.add(dir);
    });
    
    // 检查测试分散问题
    if (testLocations.size > 5) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: 'project-wide',
        description: `测试文件分散在${testLocations.size}个不同位置，策略不统一`,
        suggestion: '统一测试文件组织结构，建议采用单一测试目录或就近测试策略'
      });
    }
    
    // 检查测试命名不一致
    const testExtensions = new Set<string>();
    testFiles.forEach(file => {
      const match = file.match(/\.(test|spec)\.ts$/);
      if (match) {
        testExtensions.add(match[1]);
      }
    });
    
    if (testExtensions.size > 1) {
      this.issues.push({
        type: 'strategy',
        severity: 'low',
        file: 'project-wide',
        description: '测试文件命名不一致，同时使用了.test.ts和.spec.ts',
        suggestion: '统一测试文件命名规范，建议使用.test.ts'
      });
    }
    
    // 检查测试命名规范
    await this.analyzeTestNamingConventions(testFiles);
    
    // 检查统一测试工具库使用
    await this.analyzeUnifiedTestToolsUsage(testFiles);
    
    // 检查测试框架统一性
    await this.analyzeTestFrameworkConsistency(testFiles);
    
    // 检查测试位置策略
    await this.analyzeTestLocationStrategy(testFiles);
    
    // 检查测试配置一致性
    await this.analyzeTestConfiguration();
    
    // 检查重复的测试逻辑
    await this.detectDuplicatedTestLogic(testFiles);
  }

  private async analyzeMockQuality(): Promise<void> {
    console.log('🎭 分析Mock数据质量...');
    
    const testFiles = await glob('**/*.{test,spec}.ts');
    
    for (const file of testFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      
      // 检查Math.random()在测试中的使用
      if (content.includes('Math.random()')) {
        this.issues.push({
          type: 'mock_quality',
          severity: 'high',
          file,
          description: '测试中使用Math.random()，可能导致测试结果不稳定',
          suggestion: '使用固定种子的随机数生成器或UnifiedTestDataGenerator'
        });
      }
      
      // 检查是否使用了统一测试工具库
      const hasUnifiedTestDataGenerator = content.includes('UnifiedTestDataGenerator');
      const hasUnifiedTestAssertions = content.includes('UnifiedTestAssertions');
      const hasUnifiedIntegrationTestBase = content.includes('UnifiedIntegrationTestBase');
      
      if (!hasUnifiedTestDataGenerator && (content.includes('createMock') || content.includes('generateTest'))) {
        this.issues.push({
          type: 'mock_quality',
          severity: 'medium',
          file,
          description: '未使用统一测试数据生成器',
          suggestion: '使用UnifiedTestDataGenerator统一管理测试数据生成'
        });
      }
      
      if (!hasUnifiedTestAssertions && content.includes('expect(')) {
        this.issues.push({
          type: 'mock_quality',
          severity: 'low',
          file,
          description: '未使用统一测试断言库',
          suggestion: '考虑使用UnifiedTestAssertions提供更丰富的断言方法'
        });
      }
      
      // 检查测试数据工厂模式
      const hasTestDataFactory = content.includes('Factory') && content.includes('create');
      if (!hasTestDataFactory && content.includes('const testData')) {
        this.issues.push({
          type: 'mock_quality',
          severity: 'medium',
          file,
          description: '未使用测试数据工厂模式',
          suggestion: '使用工厂模式创建测试数据，提高测试数据的可维护性'
        });
      }
      
      // 检查硬编码的测试数据
      const hardcodedPatterns = [
        /const.*=.*\{[^}]*price.*:.*\d+/g,
        /const.*=.*\{[^}]*amount.*:.*\d+/g,
        /const.*=.*\{[^}]*timestamp.*:.*\d+/g
      ];
      
      hardcodedPatterns.forEach(pattern => {
        if (pattern.test(content)) {
          this.issues.push({
            type: 'mock_quality',
            severity: 'medium',
            file,
            description: '测试中存在硬编码的业务数据',
            suggestion: '使用测试数据工厂或fixture文件管理测试数据'
          });
        }
      });
      
      // 检查固定种子使用
      if (content.includes('Math.random') && !content.includes('seed')) {
        this.issues.push({
          type: 'mock_quality',
          severity: 'medium',
          file,
          description: '随机数生成未使用固定种子',
          suggestion: '使用固定种子确保测试结果的可重现性'
        });
      }
      
      // 检查缺少边界值测试
      if (!content.includes('edge case') && !content.includes('boundary') && !content.includes('极值')) {
        const hasBusinessLogic = content.includes('calculate') || content.includes('validate') || content.includes('process');
        if (hasBusinessLogic) {
          this.issues.push({
            type: 'mock_quality',
            severity: 'medium',
            file,
            description: '缺少边界值和异常情况测试',
            suggestion: '添加边界值、空值、异常输入的测试用例'
          });
        }
      }
    }
  }

  private async analyzeE2ECompleteness(): Promise<void> {
    console.log('🔄 分析E2E测试完整性...');
    
    const e2eFiles = await glob('**/*{e2e,integration,end-to-end}*.{test,spec}.ts');
    const routeFiles = await glob('src/**/routes/**/*.ts');
    const apiFiles = await glob('src/api/**/*.ts');
    
    // 检查关键业务流程的E2E测试
    const criticalFlows = [
      'trading', 'order', 'signal', 'risk', 'auth', 'user'
    ];
    
    for (const flow of criticalFlows) {
      const hasE2ETest = e2eFiles.some(file => 
        file.toLowerCase().includes(flow) ||
        fs.readFileSync(file, 'utf-8').toLowerCase().includes(flow)
      );
      
      if (!hasE2ETest) {
        this.issues.push({
          type: 'e2e_completeness',
          severity: 'high',
          file: 'project-wide',
          description: `缺少${flow}业务流程的端到端测试`,
          suggestion: `添加${flow}完整业务流程的集成测试`
        });
      }
    }
    
    // 检查API端点测试覆盖
    const totalApiEndpoints = routeFiles.length + apiFiles.length;
    if (e2eFiles.length < totalApiEndpoints * 0.3) {
      this.issues.push({
        type: 'e2e_completeness',
        severity: 'medium',
        file: 'project-wide',
        description: `E2E测试覆盖率不足，仅有${e2eFiles.length}个E2E测试文件，但有${totalApiEndpoints}个API文件`,
        suggestion: '增加API端点的集成测试，确保关键接口的可用性'
      });
    }
  }

  private calculateCoverage(sourceFiles: string[], testFiles: string[]): TestCoverageStats {
    let testedFiles = 0;
    const missingTests: string[] = [];
    
    for (const sourceFile of sourceFiles) {
      const hasTest = this.hasCorrespondingTest(sourceFile, testFiles);
      if (hasTest) {
        testedFiles++;
      } else {
        missingTests.push(sourceFile);
      }
    }
    
    return {
      totalFiles: sourceFiles.length,
      testedFiles,
      coveragePercentage: (testedFiles / sourceFiles.length) * 100,
      missingTests
    };
  }

  private hasCorrespondingTest(sourceFile: string, testFiles: string[]): boolean {
    const baseName = path.basename(sourceFile, '.ts');
    const dirName = path.dirname(sourceFile);
    
    return testFiles.some(testFile => {
      const testBaseName = path.basename(testFile).replace(/\.(test|spec)\.ts$/, '');
      const testDirName = path.dirname(testFile);
      
      // 检查同名测试文件
      if (testBaseName === baseName) return true;
      
      // 检查测试文件内容是否引用了源文件
      try {
        const testContent = fs.readFileSync(testFile, 'utf-8');
        return testContent.includes(baseName) || testContent.includes(sourceFile);
      } catch {
        return false;
      }
    });
  }

  private async analyzeTestFrameworkConsistency(testFiles: string[]): Promise<void> {
    const assertionLibraries = new Set<string>();
    const testRunners = new Set<string>();
    const mockLibraries = new Set<string>();
    
    for (const file of testFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      
      // 检测断言库
      if (content.includes('expect(') && content.includes('.toBe(')) assertionLibraries.add('Jest/Vitest');
      if (content.includes('assert.')) assertionLibraries.add('Node Assert');
      if (content.includes('chai.')) assertionLibraries.add('Chai');
      if (content.includes('should.')) assertionLibraries.add('Should.js');
      
      // 检测测试运行器
      if (content.includes('describe(') || content.includes('it(')) testRunners.add('Jest/Vitest/Mocha');
      if (content.includes('test(')) testRunners.add('Jest/Vitest');
      if (content.includes('suite(')) testRunners.add('Mocha');
      
      // 检测Mock库
      if (content.includes('jest.mock(') || content.includes('vi.mock(')) mockLibraries.add('Jest/Vitest');
      if (content.includes('sinon.')) mockLibraries.add('Sinon');
      if (content.includes('td.')) mockLibraries.add('Testdouble');
    }
    
    // 检查断言库统一性
    if (assertionLibraries.size > 1) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: 'project-wide',
        description: `使用了多种断言库: ${Array.from(assertionLibraries).join(', ')}`,
        suggestion: '统一使用单一断言库，建议使用Jest/Vitest的内置断言'
      });
    }
    
    // 检查Mock库统一性
    if (mockLibraries.size > 1) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: 'project-wide',
        description: `使用了多种Mock库: ${Array.from(mockLibraries).join(', ')}`,
        suggestion: '统一使用单一Mock库，建议使用测试框架内置的Mock功能'
      });
    }
  }
  
  private async analyzeTestLocationStrategy(testFiles: string[]): Promise<void> {
    const sourceFiles = await glob(`${this.srcDir}/**/*.ts`, {
      ignore: ['**/*.test.ts', '**/*.spec.ts', '**/__tests__/**', '**/tests/**']
    });
    
    let colocatedTests = 0;
    let separateTests = 0;
    
    testFiles.forEach(testFile => {
      const testDir = path.dirname(testFile);
      const isColocated = testDir.includes('src/') && !testDir.includes('tests/');
      
      if (isColocated) {
        colocatedTests++;
      } else {
        separateTests++;
      }
    });
    
    // 检查测试位置策略混合使用
    if (colocatedTests > 0 && separateTests > 0) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: 'project-wide',
        description: `测试位置策略不统一: ${colocatedTests}个就近测试，${separateTests}个集中测试`,
        suggestion: '选择统一的测试位置策略：要么全部就近放置，要么全部集中管理'
      });
    }
    
    // 检查测试文件与源文件的对应关系
    let orphanedTests = 0;
    testFiles.forEach(testFile => {
      const baseName = path.basename(testFile).replace(/\.(test|spec)\.ts$/, '');
      const hasCorrespondingSource = sourceFiles.some(sourceFile => 
        path.basename(sourceFile, '.ts') === baseName
      );
      
      if (!hasCorrespondingSource) {
        orphanedTests++;
      }
    });
    
    if (orphanedTests > testFiles.length * 0.2) {
      this.issues.push({
        type: 'strategy',
        severity: 'low',
        file: 'project-wide',
        description: `${orphanedTests}个测试文件没有对应的源文件`,
        suggestion: '检查测试文件命名是否与源文件对应，或考虑重构测试组织结构'
      });
    }
  }
  
  private async analyzeTestConfiguration(): Promise<void> {
    const configFiles = await glob('{jest,vitest,mocha}.config.{js,ts,json}');
    const packageJsonPath = 'package.json';
    
    // 检查多个测试配置文件
    if (configFiles.length > 1) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: configFiles.join(', '),
        description: `存在多个测试配置文件: ${configFiles.join(', ')}`,
        suggestion: '统一使用单一测试配置文件，避免配置冲突'
      });
    }
    
    // 检查package.json中的测试脚本
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf-8'));
      const scripts = packageJson.scripts || {};
      
      const testScripts = Object.keys(scripts).filter(key => 
        key.includes('test') || key.includes('spec')
      );
      
      // 检查测试脚本命名规范
      const hasStandardTestScript = scripts.test;
      const hasWatchScript = scripts['test:watch'] || scripts['test:dev'];
      const hasCoverageScript = scripts['test:coverage'] || scripts['test:cov'];
      
      if (!hasStandardTestScript) {
        this.issues.push({
          type: 'strategy',
          severity: 'low',
          file: 'package.json',
          description: '缺少标准的test脚本',
          suggestion: '添加"test"脚本作为主要的测试命令'
        });
      }
      
      if (!hasWatchScript && testScripts.length > 1) {
        this.issues.push({
          type: 'strategy',
          severity: 'low',
          file: 'package.json',
          description: '缺少测试监听脚本',
          suggestion: '添加"test:watch"脚本用于开发时的测试监听'
        });
      }
      
      if (!hasCoverageScript) {
        this.issues.push({
          type: 'strategy',
          severity: 'low',
          file: 'package.json',
          description: '缺少测试覆盖率脚本',
          suggestion: '添加"test:coverage"脚本用于生成测试覆盖率报告'
        });
      }
    }
  }

  private async analyzeTestNamingConventions(testFiles: string[]): Promise<void> {
    for (const file of testFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      
      // 检查中文测试用例描述
      const chineseTestPattern = /(?:describe|it|test)\(['"]([^'"]*[\u4e00-\u9fa5][^'"]*)['"],/g;
      let match;
      let hasChineseTests = false;
      
      while ((match = chineseTestPattern.exec(content)) !== null) {
        hasChineseTests = true;
        break;
      }
      
      if (!hasChineseTests) {
        // 检查是否有业务逻辑测试但缺少中文描述
        const hasBusinessTests = content.includes('should') || content.includes('when') || content.includes('given');
        if (hasBusinessTests) {
          this.issues.push({
            type: 'strategy',
            severity: 'low',
            file,
            description: '测试用例缺少中文描述，不利于业务理解',
            suggestion: '使用中文描述测试用例，提高测试的可读性和业务表达力'
          });
        }
      }
      
      // 检查驼峰命名变量
      const camelCaseVariablePattern = /(?:const|let|var)\s+([a-z][a-zA-Z0-9]*)/g;
      const snakeCaseVariablePattern = /(?:const|let|var)\s+([a-z][a-z0-9_]*_[a-z0-9_]*)/g;
      
      let camelCaseCount = 0;
      let snakeCaseCount = 0;
      
      while ((match = camelCaseVariablePattern.exec(content)) !== null) {
        camelCaseCount++;
      }
      
      while ((match = snakeCaseVariablePattern.exec(content)) !== null) {
        snakeCaseCount++;
      }
      
      // 如果混合使用命名风格
      if (camelCaseCount > 0 && snakeCaseCount > 0) {
        this.issues.push({
          type: 'strategy',
          severity: 'low',
          file,
          description: '测试文件中混合使用驼峰命名和下划线命名',
          suggestion: '统一使用驼峰命名规范，保持代码风格一致性'
        });
      }
    }
  }
  
  private async analyzeUnifiedTestToolsUsage(testFiles: string[]): Promise<void> {
    let totalTestFiles = 0;
    let unifiedDataGeneratorUsage = 0;
    let unifiedAssertionsUsage = 0;
    let unifiedIntegrationBaseUsage = 0;
    
    for (const file of testFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      totalTestFiles++;
      
      if (content.includes('UnifiedTestDataGenerator')) {
        unifiedDataGeneratorUsage++;
      }
      
      if (content.includes('UnifiedTestAssertions')) {
        unifiedAssertionsUsage++;
      }
      
      if (content.includes('UnifiedIntegrationTestBase')) {
        unifiedIntegrationBaseUsage++;
      }
    }
    
    // 检查统一测试工具库的采用率
    const dataGeneratorAdoptionRate = (unifiedDataGeneratorUsage / totalTestFiles) * 100;
    const assertionsAdoptionRate = (unifiedAssertionsUsage / totalTestFiles) * 100;
    const integrationBaseAdoptionRate = (unifiedIntegrationBaseUsage / totalTestFiles) * 100;
    
    if (dataGeneratorAdoptionRate < 30 && totalTestFiles > 10) {
      this.issues.push({
        type: 'strategy',
        severity: 'medium',
        file: 'project-wide',
        description: `UnifiedTestDataGenerator采用率过低: ${dataGeneratorAdoptionRate.toFixed(1)}%`,
        suggestion: '推广使用UnifiedTestDataGenerator统一管理测试数据生成'
      });
    }
    
    if (assertionsAdoptionRate < 20 && totalTestFiles > 10) {
      this.issues.push({
        type: 'strategy',
        severity: 'low',
        file: 'project-wide',
        description: `UnifiedTestAssertions采用率过低: ${assertionsAdoptionRate.toFixed(1)}%`,
        suggestion: '考虑使用UnifiedTestAssertions提供更丰富的断言方法'
      });
    }
    
    if (integrationBaseAdoptionRate < 50 && totalTestFiles > 5) {
      const integrationTestFiles = testFiles.filter(file => 
        file.includes('integration') || file.includes('e2e')
      );
      
      if (integrationTestFiles.length > 0) {
        this.issues.push({
          type: 'strategy',
          severity: 'medium',
          file: 'project-wide',
          description: `UnifiedIntegrationTestBase采用率过低: ${integrationBaseAdoptionRate.toFixed(1)}%`,
          suggestion: '在集成测试中使用UnifiedIntegrationTestBase统一测试基础设施'
        });
      }
    }
  }

  private async detectDuplicatedTestLogic(testFiles: string[]): Promise<void> {
    const testPatterns = new Map<string, string[]>();
    
    for (const file of testFiles) {
      const content = fs.readFileSync(file, 'utf-8');
      
      // 提取测试模式
      const patterns = [
        /describe\(['"]([^'"]+)['"],/g,
        /it\(['"]([^'"]+)['"],/g,
        /test\(['"]([^'"]+)['"],/g
      ];
      
      patterns.forEach(pattern => {
        let match;
        while ((match = pattern.exec(content)) !== null) {
          const testName = match[1];
          if (!testPatterns.has(testName)) {
            testPatterns.set(testName, []);
          }
          testPatterns.get(testName)!.push(file);
        }
      });
    }
    
    // 检查重复的测试名称
    testPatterns.forEach((files, testName) => {
      if (files.length > 1) {
        this.issues.push({
          type: 'strategy',
          severity: 'low',
          file: files.join(', '),
          description: `重复的测试名称: "${testName}"`,
          suggestion: '使用更具体的测试名称，避免测试逻辑重复'
        });
      }
    });
  }

  generateReport(): string {
    const timestamp = new Date().toLocaleString('zh-CN');
    const issuesByType = this.groupIssuesByType();
    const severityStats = this.calculateSeverityStats();
    
    let report = `# 测试质量检测报告\n\n`;
    
    // 添加脚本信息
    report += `## 脚本信息\n\n`;
    report += `**脚本名称**: 测试质量检测器 (Test Quality Detector)\n`;
    report += `**脚本路径**: \`scripts/monitoring/test-quality-detector.ts\`\n`;
    report += `**生成时间**: ${timestamp}\n`;
    report += `**检测器版本**: 1.0.0\n`;
    report += `**执行命令**: \`npm run test:quality\` 或 \`ts-node scripts/monitoring/test-quality-detector.ts\`\n\n`;
    
    // 添加标准文档引用
    report += `## 相关文档\n\n`;
    report += `📖 **测试开发标准文档**: [测试开发标准与最佳实践](./developer-guides/测试开发标准与最佳实践.md)\n\n`;
    report += `本报告基于项目测试开发标准进行检测，建议开发人员参考标准文档了解：\n`;
    report += `- 统一测试工具库的使用方法\n`;
    report += `- 测试命名规范和最佳实践\n`;
    report += `- Mock数据质量管理标准\n`;
    report += `- 集成测试和E2E测试规范\n`;
    report += `- 测试数据生成和断言标准\n\n`;
    
    // 添加脚本概述
    report += `## 检测器概述\n\n`;
    report += `测试质量检测器是一个专业的测试代码分析工具，专门用于评估TypeScript/JavaScript项目的测试质量。该检测器通过多维度分析，为开发团队提供全面的测试质量评估报告。\n\n`;
    
    report += `### 核心功能模块\n\n`;
    report += `1. **测试覆盖率分析**: 计算整体测试覆盖率，识别缺少测试的关键业务文件\n`;
    report += `   - 整体覆盖率统计：计算源文件与测试文件的对应关系\n`;
    report += `   - 关键业务文件检测：重点关注service、engine等核心模块\n`;
    report += `   - 缺失测试识别：列出所有未覆盖的源文件\n\n`;
    report += `2. **测试策略分析**: 全面检测测试组织和框架统一性\n`;
    report += `   - 测试位置策略：检测就近测试vs集中测试的一致性\n`;
    report += `   - 测试框架统一性：检测断言库、Mock库、测试运行器的统一使用\n`;
    report += `   - 测试配置一致性：验证测试配置文件和脚本的规范性\n`;
    report += `   - 测试命名规范：检查.test.ts vs .spec.ts的命名一致性\n`;
    report += `   - 中文测试描述：检测测试用例是否使用中文描述提高可读性\n`;
    report += `   - 统一测试工具库：检测UnifiedTestDataGenerator等工具的采用率\n`;
    report += `   - 命名风格一致性：检查驼峰命名与下划线命名的混合使用\n\n`;
    report += `3. **Mock数据质量分析**: 检测测试数据的质量和稳定性\n`;
    report += `   - 随机数据检测：识别Math.random()等不稳定因素\n`;
    report += `   - 硬编码数据识别：发现测试中的硬编码业务数据\n`;
    report += `   - 边界值测试检查：验证是否包含边界条件测试\n`;
    report += `   - 统一测试数据生成器：检测UnifiedTestDataGenerator的使用\n`;
    report += `   - 固定种子检测：确保随机数生成的可重现性\n`;
    report += `   - 测试数据工厂模式：检查是否使用工厂模式管理测试数据\n`;
    report += `   - 统一断言库：检测UnifiedTestAssertions的采用情况\n\n`;
    report += `4. **E2E测试完整性分析**: 评估关键业务流程的端到端测试覆盖\n`;
    report += `   - 业务流程覆盖：检查trading、order、signal等关键流程\n`;
    report += `   - API端点测试：评估API接口的集成测试覆盖率\n`;
    report += `   - 端到端测试比例：分析E2E测试与总体测试的合理比例\n\n`;
    
    report += `## 检测结果概览\n\n`;
    report += `| 指标 | 数值 |\n`;
    report += `|------|------|\n`;
    report += `| 总问题数 | ${this.issues.length} |\n`;
    report += `| 严重问题 | ${severityStats.high} |\n`;
    report += `| 中等问题 | ${severityStats.medium} |\n`;
    report += `| 轻微问题 | ${severityStats.low} |\n\n`;
    
    report += `## 问题分类统计\n\n`;
    Object.entries(issuesByType).forEach(([type, issues]) => {
      const typeNames = {
        coverage: '测试覆盖率',
        strategy: '测试策略',
        mock_quality: 'Mock数据质量',
        e2e_completeness: 'E2E测试完整性'
      };
      report += `- **${typeNames[type as keyof typeof typeNames]}**: ${issues.length}个问题\n`;
    });
    
    report += `\n## 详细问题列表\n\n`;
    
    Object.entries(issuesByType).forEach(([type, issues]) => {
      if (issues.length === 0) return;
      
      const typeNames = {
        coverage: '📊 测试覆盖率问题',
        strategy: '🎯 测试策略问题',
        mock_quality: '🎭 Mock数据质量问题',
        e2e_completeness: '🔄 E2E测试完整性问题'
      };
      
      report += `### ${typeNames[type as keyof typeof typeNames]}\n\n`;
      
      issues.forEach((issue, index) => {
        const severityEmoji = {
          high: '🔴',
          medium: '🟡',
          low: '🟢'
        };
        
        report += `#### ${index + 1}. ${severityEmoji[issue.severity]} ${issue.description}\n\n`;
        report += `**文件**: \`${issue.file}\`\n`;
        if (issue.line) {
          report += `**行号**: ${issue.line}\n`;
        }
        report += `**建议**: ${issue.suggestion}\n\n`;
      });
    });
    
    report += `## 改进建议\n\n`;
    report += `1. **提高测试覆盖率**: 为核心业务逻辑添加单元测试，目标覆盖率80%+\n`;
    report += `2. **统一测试策略**: 建立一致的测试文件组织和命名规范\n`;
    report += `3. **改善Mock质量**: 使用测试数据工厂，避免硬编码和随机数据\n`;
    report += `4. **完善E2E测试**: 为关键业务流程添加端到端测试\n`;
    report += `5. **推广统一测试工具库**: 使用UnifiedTestDataGenerator、UnifiedTestAssertions等统一工具\n`;
    report += `6. **使用中文测试描述**: 提高测试用例的业务可读性和表达力\n`;
    report += `7. **统一命名规范**: 在测试代码中统一使用驼峰命名风格\n`;
    report += `8. **固定种子机制**: 确保随机数生成的可重现性，避免测试不稳定\n`;
    report += `9. **建立测试标准**: 制定测试编写指南和代码审查清单\n`;
    report += `10. **自动化测试**: 集成测试覆盖率检查到CI/CD流程\n`;
    
    return report;
  }

  private groupIssuesByType() {
    return this.issues.reduce((acc, issue) => {
      if (!acc[issue.type]) acc[issue.type] = [];
      acc[issue.type].push(issue);
      return acc;
    }, {} as Record<string, TestQualityIssue[]>);
  }

  private calculateSeverityStats() {
    return this.issues.reduce((acc, issue) => {
      acc[issue.severity]++;
      return acc;
    }, { high: 0, medium: 0, low: 0 });
  }
}

// 主执行函数
async function main() {
  try {
    const detector = new TestQualityDetector();
    const issues = await detector.detectIssues();
    
    console.log(`\n✅ 检测完成！发现 ${issues.length} 个测试质量问题`);
    
    const report = detector.generateReport();
    const reportPath = 'docs/测试质量检测报告.md';
    
    // 确保docs目录存在
    if (!fs.existsSync('docs')) {
      fs.mkdirSync('docs', { recursive: true });
    }
    
    fs.writeFileSync(reportPath, report);
    console.log(`📄 报告已生成: ${reportPath}`);
    
    // 输出摘要
    const severityStats = issues.reduce((acc, issue) => {
      acc[issue.severity]++;
      return acc;
    }, { high: 0, medium: 0, low: 0 });
    
    console.log('\n📊 问题摘要:');
    console.log(`   🔴 严重: ${severityStats.high}`);
    console.log(`   🟡 中等: ${severityStats.medium}`);
    console.log(`   🟢 轻微: ${severityStats.low}`);
    
    if (severityStats.high > 0) {
      console.log('\n⚠️  发现严重问题，建议立即处理！');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ 检测过程中发生错误:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

export { TestQualityDetector, TestQualityIssue };