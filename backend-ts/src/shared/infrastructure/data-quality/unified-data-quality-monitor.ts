/**
 * 统一数据质量监控系统
 * 扩展现有的数据质量监控能力，实现实时数据质量评分和异常检测
 */

import { injectable, inject } from 'inversify';
import { TYPES } from '../di/types';
import { IBasicLogger } from '../logging/interfaces/basic-logger.interface';
import { UnifiedErrorHandler } from '../error/unified-error-handler';
import { MarketDataContext } from '../../../contexts/risk-management/domain/value-objects/market-data-context';
import { UnifiedDataQualityMonitor as RealTimeDataQualityMonitor } from '../../../contexts/market-data/infrastructure/external/real-time-data-quality-monitor';
import { DataAnomalyDetectionEngine } from '../../../contexts/market-data/infrastructure/external/data-anomaly-detection-engine';

/**
 * 数据质量评分权重配置
 */
export interface QualityScoreWeights {
  completeness: number;    // 完整性权重
  accuracy: number;        // 准确性权重
  consistency: number;     // 一致性权重
  timeliness: number;      // 时效性权重
  validity: number;        // 有效性权重
}

/**
 * 数据质量阈值配置
 */
export interface QualityThresholds {
  excellent: number;       // 优秀阈值 (0.9+)
  good: number;           // 良好阈值 (0.8+)
  fair: number;           // 一般阈值 (0.6+)
  poor: number;           // 较差阈值 (0.4+)
  critical: number;       // 严重阈值 (0.4-)
}

/**
 * 数据质量监控配置
 */
export interface DataQualityMonitorConfig {
  enableRealTimeMonitoring: boolean;
  enableAnomalyDetection: boolean;
  enableCrossValidation: boolean;
  enableTrendAnalysis: boolean;
  monitoringInterval: number;
  qualityScoreWeights: QualityScoreWeights;
  qualityThresholds: QualityThresholds;
  anomalyDetectionSensitivity: number;
  maxHistorySize: number;
}

/**
 * 数据质量报告
 */
export interface DataQualityReport {
  timestamp: Date;
  symbol: string;
  dataType: string;
  overallScore: number;
  qualityLevel: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'CRITICAL';
  dimensions: {
    completeness: number;
    accuracy: number;
    consistency: number;
    timeliness: number;
    validity: number;
  };
  anomalies: Array<{
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
    confidence: number;
  }>;
  recommendations: string[];
  dataProvenance: {
    sources: string[];
    verificationStatus: 'VERIFIED' | 'PENDING' | 'FAILED';
    lastVerified: Date;
  };
}

/**
 * 数据质量趋势分析
 */
export interface QualityTrendAnalysis {
  symbol: string;
  timeRange: string;
  trendDirection: 'IMPROVING' | 'STABLE' | 'DECLINING';
  averageScore: number;
  scoreVariance: number;
  qualityEvents: Array<{
    timestamp: Date;
    eventType: 'IMPROVEMENT' | 'DEGRADATION' | 'ANOMALY';
    description: string;
    impact: number;
  }>;
}

/**
 * 增强数据质量监控器
 */
@injectable()
export class EnhancedDataQualityMonitor {
  private readonly config: DataQualityMonitorConfig;
  private readonly qualityHistory: Map<string, DataQualityReport[]> = new Map();
  private readonly activeMonitors: Map<string, NodeJS.Timeout> = new Map();
  private isMonitoring = false;

  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(TYPES.MarketData.RealTimeDataQualityMonitor) private readonly realTimeMonitor: RealTimeDataQualityMonitor,
    @inject(TYPES.MarketData.DataAnomalyDetectionEngine) private readonly anomalyDetector: DataAnomalyDetectionEngine
  ) {
    this.config = {
      enableRealTimeMonitoring: true,
      enableAnomalyDetection: true,
      enableCrossValidation: true,
      enableTrendAnalysis: true,
      monitoringInterval: 30000, // 30秒
      qualityScoreWeights: {
        completeness: 0.25,
        accuracy: 0.25,
        consistency: 0.20,
        timeliness: 0.15,
        validity: 0.15
      },
      qualityThresholds: {
        excellent: 0.9,
        good: 0.8,
        fair: 0.6,
        poor: 0.4,
        critical: 0.0
      },
      anomalyDetectionSensitivity: 0.8,
      maxHistorySize: 1000
    };

    this.initializeMonitoring();
  }

  /**
   * 初始化监控
   */
  private initializeMonitoring(): void {
    this.logger.info('初始化统一数据质量监控系统', {
      realTimeMonitoring: this.config.enableRealTimeMonitoring,
      anomalyDetection: this.config.enableAnomalyDetection,
      interval: this.config.monitoringInterval
    });

    // 设置事件监听
    this.setupEventListeners();
  }

  /**
   * 设置事件监听
   */
  private setupEventListeners(): void {
    // 监听实时数据质量评估事件
    this.realTimeMonitor.on('qualityAssessed', (assessment) => {
      this.handleQualityAssessment(assessment);
    });

    // 监听异常检测事件
    this.anomalyDetector.on('anomalyDetected', (anomaly) => {
      this.handleAnomalyDetection(anomaly);
    });
  }

  /**
   * 启动数据质量监控
   */
  startMonitoring(symbols: string[] = []): void {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    this.logger.info('启动数据质量监控', { symbols: symbols.length });

    if (symbols.length === 0) {
      // 监控所有符号
      this.startGlobalMonitoring();
    } else {
      // 监控指定符号
      symbols.forEach(symbol => this.startSymbolMonitoring(symbol));
    }
  }

  /**
   * 停止数据质量监控
   */
  stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    this.isMonitoring = false;
    
    // 清理所有监控定时器
    this.activeMonitors.forEach((timer, symbol) => {
      clearInterval(timer);
      this.logger.debug('停止符号监控', { symbol });
    });
    
    this.activeMonitors.clear();
    this.logger.info('停止数据质量监控');
  }

  /**
   * 评估MarketDataContext的数据质量
   */
  async assessMarketDataQuality(context: MarketDataContext): Promise<DataQualityReport> {
    const startTime = Date.now();

    try {
      // 1. 评估各个质量维度
      const dimensions = await this.evaluateQualityDimensions(context);

      // 2. 计算总体质量评分
      const overallScore = this.calculateOverallScore(dimensions);

      // 3. 确定质量等级
      const qualityLevel = this.determineQualityLevel(overallScore);

      // 4. 检测异常
      const anomalies = await this.detectContextAnomalies(context);

      // 5. 生成改进建议
      const recommendations = this.generateRecommendations(dimensions, anomalies);

      // 6. 构建数据来源信息
      const dataProvenance = this.buildDataProvenance(context);

      const report: DataQualityReport = {
        timestamp: new Date(),
        symbol: context.symbol,
        dataType: 'market-data-context',
        overallScore,
        qualityLevel,
        dimensions,
        anomalies,
        recommendations,
        dataProvenance
      };

      // 7. 存储报告历史
      this.storeQualityReport(report);

      // 8. 更新MarketDataContext的质量信息
      this.updateContextQualityInfo(context, report);

      const processingTime = Date.now() - startTime;
      this.logger.debug('数据质量评估完成', {
        symbol: context.symbol,
        overallScore,
        qualityLevel,
        anomalies: anomalies.length,
        processingTime
      });

      return report;

    } catch (error) {
      this.logger.error('数据质量评估失败', {
        symbol: context.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * 评估质量维度
   */
  private async evaluateQualityDimensions(context: MarketDataContext): Promise<{
    completeness: number;
    accuracy: number;
    consistency: number;
    timeliness: number;
    validity: number;
  }> {
    return {
      completeness: this.evaluateCompleteness(context),
      accuracy: this.evaluateAccuracy(context),
      consistency: this.evaluateConsistency(context),
      timeliness: this.evaluateTimeliness(context),
      validity: this.evaluateValidity(context)
    };
  }

  /**
   * 评估数据完整性
   */
  private evaluateCompleteness(context: MarketDataContext): number {
    const requiredFields = [
      'symbol', 'currentPrice', 'volume24h', 'volatility', 'trend',
      'marketPhase', 'sentiment', 'change24h', 'priceHistory',
      'technicalIndicators', 'orderBook', 'timestamp'
    ];

    let completedFields = 0;
    
    requiredFields.forEach(field => {
      const value = (context as any)[field];
      if (value !== undefined && value !== null) {
        if (Array.isArray(value) && value.length > 0) {
          completedFields++;
        } else if (typeof value === 'object' && Object.keys(value).length > 0) {
          completedFields++;
        } else if (typeof value !== 'object') {
          completedFields++;
        }
      }
    });

    return completedFields / requiredFields.length;
  }

  /**
   * 评估数据准确性
   */
  private evaluateAccuracy(context: MarketDataContext): number {
    let accuracyScore = 1.0;

    // 检查价格合理性
    if (context.currentPrice <= 0) {
      accuracyScore -= 0.3;
    }

    // 检查技术指标合理性
    const rsi = context.technicalIndicators?.rsi;
    if (rsi !== undefined && (rsi < 0 || rsi > 100)) {
      accuracyScore -= 0.2;
    }

    // 检查OHLC数据一致性
    if (context.orderBook?.bids?.length > 0 && context.orderBook?.asks?.length > 0) {
      const bestBid = context.orderBook.bids[0]?.price || 0;
      const bestAsk = context.orderBook.asks[0]?.price || 0;
      
      if (bestBid >= bestAsk) {
        accuracyScore -= 0.2; // 买价不应该高于卖价
      }
    }

    // 检查成交量合理性
    if (context.volume24h < 0) {
      accuracyScore -= 0.2;
    }

    return Math.max(0, accuracyScore);
  }

  /**
   * 评估数据一致性
   */
  private evaluateConsistency(context: MarketDataContext): number {
    let consistencyScore = 1.0;

    // 检查趋势与技术指标的一致性
    const rsi = context.technicalIndicators?.rsi;
    if (rsi !== undefined) {
      if (context.trend === 'BULLISH' && rsi < 30) {
        consistencyScore -= 0.2; // 牛市趋势但RSI超卖
      } else if (context.trend === 'BEARISH' && rsi > 70) {
        consistencyScore -= 0.2; // 熊市趋势但RSI超买
      }
    }

    // 检查情绪与市场阶段的一致性
    if (context.sentiment === 'GREED' && context.marketPhase === 'MARKDOWN') {
      consistencyScore -= 0.15;
    } else if (context.sentiment === 'FEAR' && context.marketPhase === 'MARKUP') {
      consistencyScore -= 0.15;
    }

    return Math.max(0, consistencyScore);
  }

  /**
   * 评估数据时效性
   */
  private evaluateTimeliness(context: MarketDataContext): number {
    const now = Date.now();
    const dataTime = context.timestamp.getTime();
    const ageInMinutes = (now - dataTime) / (1000 * 60);

    // 数据越新，时效性评分越高
    if (ageInMinutes <= 1) return 1.0;      // 1分钟内：满分
    if (ageInMinutes <= 5) return 0.9;      // 5分钟内：90分
    if (ageInMinutes <= 15) return 0.7;     // 15分钟内：70分
    if (ageInMinutes <= 60) return 0.5;     // 1小时内：50分
    
    return Math.max(0.1, 1 - (ageInMinutes / 1440)); // 超过1小时递减
  }

  /**
   * 评估数据有效性
   */
  private evaluateValidity(context: MarketDataContext): number {
    let validityScore = 1.0;

    // 检查数据格式有效性
    if (!context.symbol || typeof context.symbol !== 'string') {
      validityScore -= 0.2;
    }

    if (typeof context.currentPrice !== 'number' || isNaN(context.currentPrice)) {
      validityScore -= 0.3;
    }

    if (typeof context.volume24h !== 'number' || isNaN(context.volume24h)) {
      validityScore -= 0.2;
    }

    // 检查枚举值有效性
    const validTrends = ['BULLISH', 'BEARISH', 'SIDEWAYS'];
    if (!validTrends.includes(context.trend)) {
      validityScore -= 0.1;
    }

    const validPhases = ['ACCUMULATION', 'MARKUP', 'DISTRIBUTION', 'MARKDOWN'];
    if (!validPhases.includes(context.marketPhase)) {
      validityScore -= 0.1;
    }

    return Math.max(0, validityScore);
  }

  /**
   * 计算总体质量评分
   */
  private calculateOverallScore(dimensions: any): number {
    const weights = this.config.qualityScoreWeights;
    
    return (
      dimensions.completeness * weights.completeness +
      dimensions.accuracy * weights.accuracy +
      dimensions.consistency * weights.consistency +
      dimensions.timeliness * weights.timeliness +
      dimensions.validity * weights.validity
    );
  }

  /**
   * 确定质量等级
   */
  private determineQualityLevel(score: number): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'POOR' | 'CRITICAL' {
    const thresholds = this.config.qualityThresholds;
    
    if (score >= thresholds.excellent) return 'EXCELLENT';
    if (score >= thresholds.good) return 'GOOD';
    if (score >= thresholds.fair) return 'FAIR';
    if (score >= thresholds.poor) return 'POOR';
    return 'CRITICAL';
  }

  /**
   * 检测上下文异常
   */
  private async detectContextAnomalies(context: MarketDataContext): Promise<Array<{
    type: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    description: string;
    confidence: number;
  }>> {
    const anomalies = [];

    // 价格异常检测
    if (context.currentPrice <= 0) {
      anomalies.push({
        type: 'INVALID_PRICE',
        severity: 'CRITICAL' as const,
        description: '价格为零或负数',
        confidence: 1.0
      });
    }

    // 成交量异常检测
    if (context.volume24h < 0) {
      anomalies.push({
        type: 'INVALID_VOLUME',
        severity: 'HIGH' as const,
        description: '24小时成交量为负数',
        confidence: 1.0
      });
    }

    // 技术指标异常检测
    const rsi = context.technicalIndicators?.rsi;
    if (rsi !== undefined && (rsi < 0 || rsi > 100)) {
      anomalies.push({
        type: 'INVALID_RSI',
        severity: 'MEDIUM' as const,
        description: `RSI值超出正常范围: ${rsi}`,
        confidence: 0.9
      });
    }

    return anomalies;
  }

  /**
   * 生成改进建议
   */
  private generateRecommendations(dimensions: any, anomalies: any[]): string[] {
    const recommendations = [];

    if (dimensions.completeness < 0.8) {
      recommendations.push('补充缺失的数据字段，特别是技术指标和订单簿数据');
    }

    if (dimensions.accuracy < 0.8) {
      recommendations.push('验证数据源的准确性，检查数据采集和处理流程');
    }

    if (dimensions.consistency < 0.8) {
      recommendations.push('检查不同数据源之间的一致性，统一数据标准');
    }

    if (dimensions.timeliness < 0.8) {
      recommendations.push('优化数据更新频率，减少数据延迟');
    }

    if (anomalies.length > 0) {
      recommendations.push('处理检测到的数据异常，建立异常数据过滤机制');
    }

    return recommendations;
  }

  /**
   * 构建数据来源信息
   */
  private buildDataProvenance(context: MarketDataContext): {
    sources: string[];
    verificationStatus: 'VERIFIED' | 'PENDING' | 'FAILED';
    lastVerified: Date;
  } {
    // 这里可以根据实际情况获取数据来源信息
    return {
      sources: ['binance', 'okx'], // 示例数据源
      verificationStatus: 'VERIFIED',
      lastVerified: new Date()
    };
  }

  /**
   * 存储质量报告
   */
  private storeQualityReport(report: DataQualityReport): void {
    const key = `${report.symbol}-${report.dataType}`;
    
    if (!this.qualityHistory.has(key)) {
      this.qualityHistory.set(key, []);
    }

    const history = this.qualityHistory.get(key)!;
    history.push(report);

    // 保持历史记录在限制范围内
    if (history.length > this.config.maxHistorySize) {
      history.splice(0, history.length - this.config.maxHistorySize);
    }
  }

  /**
   * 更新MarketDataContext的质量信息
   */
  private updateContextQualityInfo(context: MarketDataContext, report: DataQualityReport): void {
    context.qualityMetrics = {
      completeness: report.dimensions.completeness,
      accuracy: report.dimensions.accuracy,
      consistency: report.dimensions.consistency,
      timeliness: report.dimensions.timeliness,
      validity: report.dimensions.validity,
      overallScore: report.overallScore
    };

    context.dataProvenance = report.dataProvenance;

    context.anomalyFlags = {
      hasAnomalies: report.anomalies.length > 0,
      anomalyTypes: report.anomalies.map(a => a.type),
      anomalySeverity: report.anomalies.length > 0 ? 
        report.anomalies.reduce((max, a) => 
          this.compareSeverity(a.severity, max) > 0 ? a.severity : max, 'LOW' as const
        ) : 'LOW',
      anomalyDetails: report.anomalies
    };

    // 更新原有的dataQuality字段
    context.dataQuality = report.qualityLevel === 'EXCELLENT' || report.qualityLevel === 'GOOD' ? 'HIGH' :
                         report.qualityLevel === 'FAIR' ? 'MEDIUM' : 'LOW';
  }

  /**
   * 比较异常严重程度
   */
  private compareSeverity(a: string, b: string): number {
    const severityOrder = { 'LOW': 1, 'MEDIUM': 2, 'HIGH': 3, 'CRITICAL': 4 };
    return (severityOrder[a as keyof typeof severityOrder] || 0) - 
           (severityOrder[b as keyof typeof severityOrder] || 0);
  }

  /**
   * 启动全局监控
   */
  private startGlobalMonitoring(): void {
    const timer = setInterval(() => {
      this.performGlobalQualityCheck();
    }, this.config.monitoringInterval);

    this.activeMonitors.set('global', timer);
    this.logger.info('启动全局数据质量监控');
  }

  /**
   * 启动符号监控
   */
  private startSymbolMonitoring(symbol: string): void {
    const timer = setInterval(() => {
      this.performSymbolQualityCheck(symbol);
    }, this.config.monitoringInterval);

    this.activeMonitors.set(symbol, timer);
    this.logger.debug('启动符号监控', { symbol });
  }

  /**
   * 执行全局质量检查
   */
  private async performGlobalQualityCheck(): Promise<void> {
    try {
      this.logger.debug('执行全局数据质量检查');

      // 这里可以实现全局质量检查逻辑
      // 例如：检查系统整体数据质量状态

    } catch (error) {
      this.logger.error('全局质量检查失败', { error });
    }
  }

  /**
   * 执行符号质量检查
   */
  private async performSymbolQualityCheck(symbol: string): Promise<void> {
    try {
      this.logger.debug('执行符号质量检查', { symbol });

      // 这里可以实现特定符号的质量检查逻辑

    } catch (error) {
      this.logger.error('符号质量检查失败', { symbol, error });
    }
  }

  /**
   * 处理质量评估事件
   */
  private handleQualityAssessment(assessment: any): void {
    this.logger.debug('处理质量评估事件', {
      eventId: assessment.eventId,
      overallScore: assessment.overallScore
    });

    // 可以在这里添加质量评估后的处理逻辑
  }

  /**
   * 处理异常检测事件
   */
  private handleAnomalyDetection(anomaly: any): void {
    this.logger.warn('检测到数据异常', {
      anomalyId: anomaly.id,
      type: anomaly.type,
      severity: anomaly.severity
    });

    // 可以在这里添加异常处理逻辑
  }

  /**
   * 获取质量历史记录
   */
  getQualityHistory(symbol: string, dataType: string = 'market-data-context'): DataQualityReport[] {
    const key = `${symbol}-${dataType}`;
    return this.qualityHistory.get(key) || [];
  }

  /**
   * 获取最新质量报告
   */
  getLatestQualityReport(symbol: string, dataType: string = 'market-data-context'): DataQualityReport | null {
    const history = this.getQualityHistory(symbol, dataType);
    return history.length > 0 ? history[history.length - 1] : null;
  }

  /**
   * 获取质量趋势分析
   */
  async getQualityTrend(symbol: string, timeRange: string = '24h'): Promise<QualityTrendAnalysis> {
    const history = this.getQualityHistory(symbol);

    if (history.length < 2) {
      return {
        symbol,
        timeRange,
        trendDirection: 'STABLE',
        averageScore: history.length > 0 ? history[0].overallScore : 0,
        scoreVariance: 0,
        qualityEvents: []
      };
    }

    // 计算平均分数和方差
    const scores = history.map(h => h.overallScore);
    const averageScore = scores.reduce((a, b) => a + b, 0) / scores.length;
    const scoreVariance = scores.reduce((sum, score) => sum + Math.pow(score - averageScore, 2), 0) / scores.length;

    // 分析趋势方向
    const recentScores = scores.slice(-5); // 最近5个数据点
    const trendDirection = this.analyzeTrendDirection(recentScores);

    // 识别质量事件
    const qualityEvents = this.identifyQualityEvents(history);

    return {
      symbol,
      timeRange,
      trendDirection,
      averageScore,
      scoreVariance,
      qualityEvents
    };
  }

  /**
   * 分析趋势方向
   */
  private analyzeTrendDirection(scores: number[]): 'IMPROVING' | 'STABLE' | 'DECLINING' {
    if (scores.length < 2) return 'STABLE';

    const firstHalf = scores.slice(0, Math.floor(scores.length / 2));
    const secondHalf = scores.slice(Math.floor(scores.length / 2));

    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

    const difference = secondAvg - firstAvg;

    if (difference > 0.05) return 'IMPROVING';
    if (difference < -0.05) return 'DECLINING';
    return 'STABLE';
  }

  /**
   * 识别质量事件
   */
  private identifyQualityEvents(history: DataQualityReport[]): Array<{
    timestamp: Date;
    eventType: 'IMPROVEMENT' | 'DEGRADATION' | 'ANOMALY';
    description: string;
    impact: number;
  }> {
    const events = [];

    for (let i = 1; i < history.length; i++) {
      const current = history[i];
      const previous = history[i - 1];
      const scoreDiff = current.overallScore - previous.overallScore;

      if (Math.abs(scoreDiff) > 0.1) {
        events.push({
          timestamp: current.timestamp,
          eventType: scoreDiff > 0 ? 'IMPROVEMENT' : 'DEGRADATION',
          description: `质量评分${scoreDiff > 0 ? '提升' : '下降'} ${Math.abs(scoreDiff * 100).toFixed(1)}%`,
          impact: Math.abs(scoreDiff)
        });
      }

      if (current.anomalies.length > previous.anomalies.length) {
        events.push({
          timestamp: current.timestamp,
          eventType: 'ANOMALY',
          description: `检测到${current.anomalies.length - previous.anomalies.length}个新异常`,
          impact: (current.anomalies.length - previous.anomalies.length) * 0.1
        });
      }
    }

    return events.slice(-10); // 返回最近10个事件
  }

  /**
   * 获取质量统计信息
   */
  getQualityStatistics(): {
    totalReports: number;
    averageScore: number;
    qualityDistribution: Record<string, number>;
    topIssues: Array<{ type: string; count: number; severity: string }>;
  } {
    let totalReports = 0;
    let totalScore = 0;
    const qualityDistribution: Record<string, number> = {
      EXCELLENT: 0,
      GOOD: 0,
      FAIR: 0,
      POOR: 0,
      CRITICAL: 0
    };
    const issueMap = new Map<string, { count: number; severity: string }>();

    this.qualityHistory.forEach(reports => {
      reports.forEach(report => {
        totalReports++;
        totalScore += report.overallScore;
        qualityDistribution[report.qualityLevel]++;

        report.anomalies.forEach(anomaly => {
          const key = anomaly.type;
          if (issueMap.has(key)) {
            issueMap.get(key)!.count++;
          } else {
            issueMap.set(key, { count: 1, severity: anomaly.severity });
          }
        });
      });
    });

    const topIssues = Array.from(issueMap.entries())
      .map(([type, data]) => ({ type, ...data }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    return {
      totalReports,
      averageScore: totalReports > 0 ? totalScore / totalReports : 0,
      qualityDistribution,
      topIssues
    };
  }

  /**
   * 清理历史数据
   */
  cleanupHistory(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    const cutoffTime = Date.now() - maxAge;
    let cleanedCount = 0;

    this.qualityHistory.forEach((reports, key) => {
      const filteredReports = reports.filter(report =>
        report.timestamp.getTime() > cutoffTime
      );

      if (filteredReports.length !== reports.length) {
        cleanedCount += reports.length - filteredReports.length;
        this.qualityHistory.set(key, filteredReports);
      }
    });

    if (cleanedCount > 0) {
      this.logger.info('清理历史质量数据', { cleanedCount });
    }
  }

  /**
   * 销毁资源
   */
  destroy(): void {
    this.stopMonitoring();
    this.qualityHistory.clear();
    this.logger.info('UnifiedDataQualityMonitor 资源已清理');
  }
}
