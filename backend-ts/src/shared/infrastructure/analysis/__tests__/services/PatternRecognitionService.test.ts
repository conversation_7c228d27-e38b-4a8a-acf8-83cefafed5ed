/**
 * 模式识别服务单元测试
 */

import 'reflect-metadata';
import { PatternRecognitionService } from '../../services/PatternRecognitionService';
import { ILogger } from '../../../../../shared/infrastructure/logging/logger.interface';
import {
  KlineDataPoint,
  PatternType,
  PatternRecognitionResult,
  PatternSearchQuery,
  PatternMonitoringConfig
} from '../../types/PatternTypes';
import { TradingSymbol } from '../../../../../contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '../../../../../contexts/market-data/domain/value-objects/timeframe';

// 使用统一测试工具库
import { 
  UnifiedTestDataGenerator, 
  UnifiedTestAssertions, 
  TestUtils 
} from '../../../../../shared/infrastructure/testing';

// 创建测试数据生成器实例（使用固定种子确保可重现性）
const testDataGenerator = TestUtils.createDataGenerator(16572);

// Mock Logger
// 使用统一的Mock Logger创建器
const mockLogger: ILogger = TestUtils.getConfigManager().createMockLogger();

describe('PatternRecognitionService', () => {
  let service: PatternRecognitionService;

  beforeEach(() => {
    vi.clearAllMocks();
    service = new PatternRecognitionService(mockLogger);
  });

  // 生成测试用K线数据
  const generateMockKlines = (count: number, trend: 'up' | 'down' | 'sideways' = 'sideways'): KlineDataPoint[] => {
    const klines: KlineDataPoint[] = [];
    let basePrice = 50000;
    const now = new Date();

    for (let i = 0; i < count; i++) {
      const timestamp = new Date(now.getTime() - (count - i) * 60000);
      
      let change = 0;
      if (trend === 'up') {
        change = (Math.random() * 0.02) + 0.005; // 0.5% - 2.5% 上涨
      } else if (trend === 'down') {
        change = -(Math.random() * 0.02) - 0.005; // 0.5% - 2.5% 下跌
      } else {
        change = (Math.random() - 0.5) * 0.02; // -1% 到 1% 震荡
      }

      const open = basePrice;
      const close = basePrice * (1 + change);
      const high = Math.max(open, close) * (1 + Math.random() * 0.01);
      const low = Math.min(open, close) * (1 - Math.random() * 0.01);
      const volume = 1000 + Math.random() * 5000;

      klines.push({
        timestamp,
        open,
        high,
        low,
        close,
        volume
      });

      basePrice = close;
    }

    return klines;
  };

  describe('初始化', () => {
    it('应该正确初始化服务', () => {
      expect(service).toBeDefined();
      expect(service.getSupportedPatterns()).toContain(PatternType.DOUBLE_TOP);
      expect(service.getSupportedPatterns()).toContain(PatternType.DOJI);
    });

    it('应该有默认配置', () => {
      const config = service.getConfig();
      
      expect(config.defaultConfidenceThreshold).toBe(0.6);
      expect(config.maxLookbackPeriod).toBe(200);
      expect(config.volumeConfirmationRequired).toBe(true);
    });
  });

  describe('recognize', () => {
    it('应该识别出形态', async () => {
      const klines = generateMockKlines(50, 'up');
      const patterns = await service.recognize(klines);

      expect(Array.isArray(patterns)).toBe(true);
      // 由于是模拟数据，可能不会识别出特定形态，但应该返回数组
    });

    it('应该在数据不足时返回空数组', async () => {
      const klines = testDataGenerator.generateKlineData(10, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    }); // 少于20个数据点
      const patterns = await service.recognize(klines);

      expect(patterns).toEqual([]);
    });

    it('应该支持指定形态类型', async () => {
      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const patterns = await service.recognize(klines, [PatternType.DOJI]);

      expect(Array.isArray(patterns)).toBe(true);
      // 如果有识别出的形态，应该只包含DOJI类型
      patterns.forEach(pattern => {
        expect(pattern.type).toBe(PatternType.DOJI);
      });
    });

    it('应该按置信度排序', async () => {
      const klines = testDataGenerator.generateKlineData(100, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const patterns = await service.recognize(klines);

      if (patterns.length > 1) {
        for (let i = 1; i < patterns.length; i++) {
          expect(patterns[i-1].confidence).toBeGreaterThanOrEqual(patterns[i].confidence);
        }
      }
    });

    it('应该处理错误情况', async () => {
      const invalidKlines: KlineDataPoint[] = [];
      const patterns = await service.recognize(invalidKlines);

      expect(patterns).toEqual([]);
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });

  describe('recognizePattern', () => {
    it('应该识别十字星形态', async () => {
      // 创建十字星K线数据
      const dojiKlines: KlineDataPoint[] = [
        {
          timestamp: new Date(),
          open: 50000,
          high: 50100,
          low: 49900,
          close: 50005, // 接近开盘价
          volume: 1000
        }
      ];

      const pattern = await service.recognizePattern(dojiKlines, PatternType.DOJI);

      if (pattern) {
        expect(pattern.type).toBe(PatternType.DOJI);
        expect(pattern.signal).toBe('neutral');
        expect(pattern.confidence).toBeGreaterThan(0);
      }
    });

    it('应该识别锤子线形态', async () => {
      // 创建锤子线K线数据
      const hammerKlines: KlineDataPoint[] = [
        {
          timestamp: new Date(),
          open: 50000,
          high: 50050,
          low: 49800, // 长下影线
          close: 49980,
          volume: 1000
        }
      ];

      const pattern = await service.recognizePattern(hammerKlines, PatternType.HAMMER);

      if (pattern) {
        expect(pattern.type).toBe(PatternType.HAMMER);
        expect(pattern.signal).toBe('bullish');
      }
    });

    it('应该在不匹配时返回null', async () => {
      const normalKlines = testDataGenerator.generateKlineData(1, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const pattern = await service.recognizePattern(normalKlines, PatternType.DOUBLE_TOP);

      expect(pattern).toBeNull();
    });

    it('应该处理不支持的形态类型', async () => {
      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const pattern = await service.recognizePattern(klines, 'unsupported' as PatternType);

      expect(pattern).toBeNull();
      expect(mockLogger.warn).toHaveBeenCalled();
    });
  });

  describe('validatePattern', () => {
    const mockPattern: PatternRecognitionResult = {
      type: PatternType.DOJI,
      name: '十字星',
      description: '测试形态',
      timeframe: new Timeframe('1h'),
      startTime: new Date(Date.now() - 3600000), // 1小时前
      endTime: new Date(),
      duration: 60,
      keyPoints: [],
      confidence: 0.8,
      completeness: 0.9,
      clarity: 0.85,
      signal: 'neutral',
      strength: 6,
      reliability: 0.8,
      priceTargets: {
        bullish: [],
        bearish: [],
        stopLoss: 50000
      },
      volumeConfirmation: {
        hasVolumeSupport: true,
        volumePattern: 'stable',
        volumeStrength: 0.7
      },
      marketContext: {
        overallTrend: 'sideways',
        volatility: 'medium',
        liquidity: 'high'
      }
    };

    it('应该验证有效形态', async () => {
      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const isValid = await service.validatePattern(mockPattern, klines);

      expect(isValid).toBe(true);
    });

    it('应该拒绝过期形态', async () => {
      const expiredPattern = {
        ...mockPattern,
        endTime: new Date(Date.now() - 25 * 60 * 60 * 1000) // 25小时前
      };

      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const isValid = await service.validatePattern(expiredPattern, klines);

      expect(isValid).toBe(false);
    });

    it('应该拒绝低置信度形态', async () => {
      const lowConfidencePattern = {
        ...mockPattern,
        confidence: 0.3 // 低于默认阈值0.6
      };

      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const isValid = await service.validatePattern(lowConfidencePattern, klines);

      expect(isValid).toBe(false);
    });

    it('应该拒绝低完整性形态', async () => {
      const lowCompletenessPattern = {
        ...mockPattern,
        completeness: 0.5 // 低于0.6
      };

      const klines = testDataGenerator.generateKlineData(50, {
      basePrice: 50000,
      volatility: 0.02,
      trend: 'sideways',
      timeInterval: 60000 // 1分钟间隔
    });
      const isValid = await service.validatePattern(lowCompletenessPattern, klines);

      expect(isValid).toBe(false);
    });
  });

  describe('startPatternMonitoring', () => {
    const mockConfig: PatternMonitoringConfig = {
      enabledPatterns: [PatternType.DOJI, PatternType.HAMMER],
      confidenceThreshold: 0.7,
      monitoringTimeframes: [new Timeframe('1h')],
      alertOnDetection: true,
      maxLookbackPeriod: 100
    };

    it('应该启动监控并返回ID', async () => {
      const monitoringId = await service.startPatternMonitoring(mockConfig);

      expect(typeof monitoringId).toBe('string');
      expect(monitoringId).toMatch(/^monitor_/);
    });

    it('应该记录监控启动日志', async () => {
      await service.startPatternMonitoring(mockConfig);

      expect(mockLogger.info).toHaveBeenCalledWith(
        '形态监控已启动',
        expect.objectContaining({
          monitoringId: expect.any(String),
          config: mockConfig
        })
      );
    });
  });

  describe('stopPatternMonitoring', () => {
    it('应该停止存在的监控', async () => {
      const monitoringId = await service.startPatternMonitoring({
        enabledPatterns: [PatternType.DOJI],
        confidenceThreshold: 0.7,
        monitoringTimeframes: [new Timeframe('1h')],
        alertOnDetection: true,
        maxLookbackPeriod: 100
      });

      await service.stopPatternMonitoring(monitoringId);

      expect(mockLogger.info).toHaveBeenCalledWith(
        '形态监控已停止',
        { monitoringId }
      );
    });

    it('应该处理不存在的监控ID', async () => {
      await service.stopPatternMonitoring('nonexistent');

      expect(mockLogger.warn).toHaveBeenCalledWith(
        '监控任务不存在',
        { monitoringId: 'nonexistent' }
      );
    });
  });

  describe('getPatternDescription', () => {
    it('应该返回双顶形态描述', () => {
      const description = service.getPatternDescription(PatternType.DOUBLE_TOP);

      expect(description.name).toBe('双顶');
      expect(description.description).toContain('两个相近的高点');
      expect(description.tradingImplications).toContain('看跌');
    });

    it('应该返回十字星形态描述', () => {
      const description = service.getPatternDescription(PatternType.DOJI);

      expect(description.name).toBe('十字星');
      expect(description.description).toContain('开盘价和收盘价几乎相等');
      expect(description.tradingImplications).toContain('转向信号');
    });

    it('应该处理未知形态类型', () => {
      const description = service.getPatternDescription('unknown' as PatternType);

      expect(description.name).toBe('未知形态');
      expect(description.description).toBe('暂无描述');
    });
  });

  describe('calculatePatternSuccessRate', () => {
    it('应该返回估算成功率', async () => {
      const successRate = await service.calculatePatternSuccessRate(
        PatternType.DOUBLE_TOP,
        '1h',
        30
      );

      expect(successRate).toBeGreaterThan(0);
      expect(successRate).toBeLessThanOrEqual(1);
    });

    it('应该为不同形态返回不同成功率', async () => {
      const doubleTopRate = await service.calculatePatternSuccessRate(
        PatternType.DOUBLE_TOP,
        '1h',
        30
      );
      
      const flagRate = await service.calculatePatternSuccessRate(
        PatternType.FLAG_BULLISH,
        '1h',
        30
      );

      // 旗形形态通常成功率更高
      expect(flagRate).toBeGreaterThan(doubleTopRate);
    });
  });

  describe('配置管理', () => {
    it('应该支持更新配置', () => {
      const newConfig = {
        confidenceThreshold: 0.8,
        volumeConfirmationRequired: false
      };

      service.updateConfig(newConfig);
      const updatedConfig = service.getConfig();

      expect(updatedConfig.defaultConfidenceThreshold).toBe(0.8);
      expect(updatedConfig.volumeConfirmationRequired).toBe(false);
    });

    it('应该保留未更新的配置项', () => {
      const originalConfig = service.getConfig();
      const originalLookback = originalConfig.maxLookbackPeriod;

      service.updateConfig({ confidenceThreshold: 0.8 });
      const updatedConfig = service.getConfig();

      expect(updatedConfig.maxLookbackPeriod).toBe(originalLookback);
    });
  });
});
