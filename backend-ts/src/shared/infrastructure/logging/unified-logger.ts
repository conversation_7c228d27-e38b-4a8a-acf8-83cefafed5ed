import * as winston from 'winston';
import { Logger, LoggerOptions, format } from 'winston';
import { injectable } from 'inversify';
import { ILogger } from './logger.interface';

/**
 * 日志级别
 */
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn',
  INFO = 'info',
  DEBUG = 'debug'
}

/**
 * 日志上下文接口
 */
export interface LogContext {
  requestId?: string;
  userId?: string;
  sessionId?: string;
  operation?: string;
  component?: string;
  duration?: number;
  [key: string]: any;
}

/**
 * 结构化日志条目
 */
export interface StructuredLogEntry {
  timestamp: string;
  level: LogLevel;
  message: string;
  service: string;
  component?: string;
  context?: LogContext;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  performance?: {
    duration: number;
    operation: string;
    status: 'success' | 'error' | 'timeout';
  };
  metadata?: {
    version: string;
    environment: string;
    nodeId: string;
  };
}

/**
 * 性能日志选项
 */
export interface PerformanceLogOptions {
  operation: string;
  threshold?: number; // 毫秒，超过此值记录为慢操作
  includeMemory?: boolean;
  includeCpu?: boolean;
}

/**
 * 统一日志管理器
 * 提供标准化的日志格式和功能
 */
@injectable()
export class UnifiedLogger implements ILogger {
  private readonly logger: Logger;
  private readonly serviceName: string;
  private readonly environment: string;
  private readonly version: string;
  private readonly nodeId: string;

  constructor(serviceName: string = 'crypto-monitor') {
    this.serviceName = serviceName;
    this.environment = process.env.NODE_ENV || 'development';
    this.version = process.env.npmPackageVersion || '1.0.0';
    this.nodeId = process.env.NODE_ID || require('os').hostname();

    this.logger = this.createLogger();
  }

  /**
   * 格式化关键上下文信息（用于控制台显示）
   */
  private formatKeyContext(entry: any): string {
    if (!entry.context && !entry.endpoint && !entry.statusCode && !entry.error) {
      return '';
    }

    const keyInfo: string[] = [];

    // API端点信息
    if (entry.endpoint) {
      keyInfo.push(`endpoint: ${entry.endpoint}`);
    }

    // HTTP状态码
    if (entry.statusCode) {
      keyInfo.push(`status: ${entry.statusCode}`);
    }

    // 响应时间
    if (entry.responseTime) {
      keyInfo.push(`time: ${entry.responseTime}ms`);
    }

    // 错误信息（简化）
    if (entry.error && typeof entry.error === 'string') {
      keyInfo.push(`error: ${entry.error.substring(0, 100)}`);
    }

    // 从上下文中提取关键信息
    if (entry.context) {
      if (entry.context.endpoint) keyInfo.push(`endpoint: ${entry.context.endpoint}`);
      if (entry.context.statusCode) keyInfo.push(`status: ${entry.context.statusCode}`);
      if (entry.context.responseTime) keyInfo.push(`time: ${entry.context.responseTime}ms`);
    }

    return keyInfo.length > 0 ? ` [${keyInfo.join(', ')}]` : '';
  }

  /**
   * 创建Winston日志器
   */
  private createLogger(): Logger {
    const isDevelopment = this.environment === 'development';
    const isProduction = this.environment === 'production';

    const logFormat = format.combine(
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      format.errors({ stack: true }),
      format.json(),
      format.printf((info) => {
        const entry: StructuredLogEntry = {
          timestamp: String(info.timestamp),
          level: info.level as LogLevel,
          message: String(info.message),
          service: this.serviceName,
          component: info.component ? String(info.component) : undefined,
          context: info.context as LogContext | undefined,
          error: info.error as StructuredLogEntry['error'],
          performance: info.performance as StructuredLogEntry['performance'],
          metadata: {
            version: this.version,
            environment: this.environment,
            nodeId: this.nodeId
          }
        };

        // 在开发环境下提供更友好的格式
        if (isDevelopment) {
          const contextStr = entry.context ? ` [${JSON.stringify(entry.context)}]` : '';
          const errorStr = entry.error ? ` ERROR: ${entry.error.message}` : '';
          const perfStr = entry.performance ? ` (${entry.performance.duration}ms)` : '';
          
          return `${entry.timestamp} [${entry.level.toUpperCase()}] ${entry.service}${entry.component ? `::${entry.component}` : ''}: ${entry.message}${contextStr}${errorStr}${perfStr}`;
        }

        return JSON.stringify(entry);
      })
    );

    // 控制台传输器 - 只显示警告和错误的简要信息
    const consoleFormat = format.combine(
      format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss.SSS' }),
      format.errors({ stack: false }), // 不在控制台显示堆栈跟踪
      format.printf((entry) => {
        // 简化的控制台输出格式，但保留关键上下文信息
        const level = entry.level.toUpperCase();
        const service = entry.service || this.serviceName;
        const component = entry.component ? `::${entry.component}` : '';

        // 对于错误，显示简要信息和关键上下文，不显示堆栈跟踪
        if (entry.level === 'error' && entry.error) {
          const contextStr = this.formatKeyContext(entry);
          const errorMessage = entry.error instanceof Error ? entry.error.message : String(entry.error);
          return `${entry.timestamp} [${level}] ${service}${component}: ${entry.message} - ${errorMessage}${contextStr}`;
        }

        // 对于警告，显示关键上下文信息
        if (entry.level === 'warn') {
          const contextStr = this.formatKeyContext(entry);
          return `${entry.timestamp} [${level}] ${service}${component}: ${entry.message}${contextStr}`;
        }

        return `${entry.timestamp} [${level}] ${service}${component}: ${entry.message}`;
      })
    );

    const transports: winston.transport[] = [
      new winston.transports.Console({
        level: process.env.LOG_LEVEL || (isDevelopment ? 'warn' : 'error'), // 默认只显示警告和错误
        format: consoleFormat
      })
    ];

    // 开发和生产环境都添加文件日志（保存详细信息用于查询）
    if (isDevelopment || isProduction) {
      // 确保logs目录存在
      const fs = require('fs');
      const path = require('path');
      const logsDir = path.join(process.cwd(), 'logs');
      if (!fs.existsSync(logsDir)) {
        fs.mkdirSync(logsDir, { recursive: true });
      }

      transports.push(
        // 错误日志文件 - 包含完整的错误信息和堆栈跟踪
        new winston.transports.File({
          filename: 'logs/error.log',
          level: 'error',
          format: logFormat, // 使用完整格式，包含堆栈跟踪
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5
        }),
        // 详细日志文件 - 包含所有级别的详细信息
        new winston.transports.File({
          filename: 'logs/detailed.log',
          level: 'debug', // 保存所有详细信息
          format: logFormat, // 使用完整格式
          maxsize: 50 * 1024 * 1024, // 50MB
          maxFiles: 10
        })
      );
    }

    const loggerOptions: LoggerOptions = {
      level: 'debug', // 日志器本身支持所有级别，由传输器控制显示
      format: logFormat,
      transports,
      exitOnError: false,
      silent: process.env.NODE_ENV === 'test'
    };

    return winston.createLogger(loggerOptions);
  }

  /**
   * 创建组件日志器
   */
  createComponentLogger(componentName: string): ComponentLogger {
    return new ComponentLogger(this, componentName);
  }

  /**
   * 记录信息日志
   */
  info(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.logger.info(message, { context });
  }

  /**
   * 记录警告日志
   */
  warn(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.logger.warn(message, { context });
  }

  /**
   * 记录错误日志
   */
  error(message: string, ...meta: any[]): void {
    const error = meta.length > 0 ? meta[0] : undefined;
    const context = meta.length > 1 ? meta[1] : undefined;
    const errorInfo = this.formatError(error);
    this.logger.error(message, {
      context,
      error: errorInfo
    });
  }

  /**
   * 记录调试日志
   */
  debug(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.logger.debug(message, { context });
  }

  /**
   * 记录性能日志
   */
  performance(message: string, duration: number, options: PerformanceLogOptions, context?: LogContext): void {
    const isSlowOperation = options.threshold && duration > options.threshold;
    const level = isSlowOperation ? LogLevel.WARN : LogLevel.INFO;
    
    const performanceData = {
      duration,
      operation: options.operation,
      status: 'success' as const
    };

    // 添加系统资源信息
    if (options.includeMemory || options.includeCpu) {
      const memUsage = process.memoryUsage();
      (performanceData as any).resources = {};
      
      if (options.includeMemory) {
        (performanceData as any).resources.memory = {
          heapUsed: memUsage.heapUsed,
          heapTotal: memUsage.heapTotal,
          rss: memUsage.rss
        };
      }
    }

    this.logger.log(level, message, {
      context,
      performance: performanceData
    });
  }

  /**
   * 记录操作开始
   */
  startOperation(operation: string, context?: LogContext): OperationTimer {
    return new OperationTimer(this, operation, context);
  }

  /**
   * 记录HTTP请求
   */
  httpRequest(method: string, url: string, statusCode: number, duration: number, context?: LogContext): void {
    const level = statusCode >= 400 ? LogLevel.WARN : LogLevel.INFO;
    const message = `${method} ${url} ${statusCode}`;
    
    this.logger.log(level, message, {
      context: {
        ...context,
        httpMethod: method,
        httpUrl: url,
        httpStatusCode: statusCode,
        httpDuration: duration
      },
      performance: {
        duration,
        operation: 'httpRequest',
        status: statusCode < 400 ? 'success' : 'error'
      }
    });
  }

  /**
   * 记录数据库操作
   */
  databaseOperation(operation: string, table: string, duration: number, context?: LogContext): void {
    const isSlowQuery = duration > 1000; // 1秒以上为慢查询
    const level = isSlowQuery ? LogLevel.WARN : LogLevel.DEBUG;
    const message = `Database ${operation} on ${table}`;
    
    this.logger.log(level, message, {
      context: {
        ...context,
        dbOperation: operation,
        dbTable: table,
        dbDuration: duration
      },
      performance: {
        duration,
        operation: 'databaseOperation',
        status: 'success'
      }
    });
  }

  /**
   * 格式化错误信息
   */
  private formatError(error: Error | unknown): any {
    if (!error) return undefined;

    if (error instanceof Error) {
      return {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      };
    }

    if (typeof error === 'string') {
      return {
        name: 'StringError',
        message: error
      };
    }

    if (typeof error === 'object') {
      return {
        name: 'ObjectError',
        message: JSON.stringify(error)
      };
    }

    return {
      name: 'UnknownError',
      message: String(error)
    };
  }

  // ILogger接口要求的方法

  /**
   * 通用日志方法
   */
  log(level: string, message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.logger.log(level, message, { context });
  }

  /**
   * 静默属性
   */
  get silent(): boolean {
    return this.logger.silent;
  }

  /**
   * 检查日志级别是否启用
   */
  isLevelEnabled(level: string): boolean {
    return this.logger.isLevelEnabled(level);
  }

  /**
   * 检查debug级别是否启用
   */
  isDebugEnabled(): boolean {
    return this.logger.isDebugEnabled();
  }

  /**
   * 检查info级别是否启用
   */
  isInfoEnabled(): boolean {
    return this.logger.isInfoEnabled();
  }

  /**
   * 检查warn级别是否启用
   */
  isWarnEnabled(): boolean {
    return this.logger.isWarnEnabled();
  }

  /**
   * 检查error级别是否启用
   */
  isErrorEnabled(): boolean {
    return this.logger.isErrorEnabled();
  }

  /**
   * 创建子日志器
   */
  child(options?: object): ILogger {
    const childLogger = this.logger.child(options);
    const unifiedLogger = new UnifiedLogger(this.serviceName);
    (unifiedLogger as any).logger = childLogger;
    return unifiedLogger;
  }

  /**
   * 获取系统指标
   */
  getSystemMetrics(): Promise<{ [key: string]: number }> {
    const memUsage = process.memoryUsage();

    return Promise.resolve({
      memoryRss: memUsage.rss,
      memoryHeapUsed: memUsage.heapUsed,
      memoryHeapTotal: memUsage.heapTotal,
      uptimeSeconds: process.uptime(),
      activeHandles: (process as any)._getActiveHandles().length,
      activeRequests: (process as any)._getActiveRequests().length
    });
  }
}

/**
 * 类型守卫：检查是否为UnifiedLogger
 */
export function isUnifiedLogger(logger: ILogger): logger is UnifiedLogger {
  return logger instanceof UnifiedLogger;
}

/**
 * 工具函数：从ILogger获取UnifiedLogger功能
 */
export function getUnifiedLoggerFeatures(logger: ILogger): {
  createComponentLogger: (name: string) => ComponentLogger;
  performance: (message: string, duration: number, options: PerformanceLogOptions, context?: LogContext) => void;
  startOperation: (operation: string, context?: LogContext) => OperationTimer;
} {
  if (isUnifiedLogger(logger)) {
    return {
      createComponentLogger: (name: string) => logger.createComponentLogger(name),
      performance: (message: string, duration: number, options: PerformanceLogOptions, context?: LogContext) =>
        logger.performance(message, duration, options, context),
      startOperation: (operation: string, context?: LogContext) => logger.startOperation(operation, context)
    };
  }

  // 为非UnifiedLogger提供默认实现
  return {
    createComponentLogger: (name: string) => new ComponentLogger(logger as any, name),
    performance: (message: string, duration: number, options: PerformanceLogOptions, context?: LogContext) => {
      logger.info(`Performance: ${message} (${duration}ms)`, context);
    },
    startOperation: (operation: string, context?: LogContext) => {
      return new OperationTimer(logger as any, operation, context);
    }
  };
}

/**
 * 组件日志器
 * 为特定组件提供预配置的日志功能
 * 完全实现ILogger接口以确保类型兼容性
 */
export class ComponentLogger implements ILogger {
  constructor(
    private readonly unifiedLogger: ILogger,
    private readonly componentName: string
  ) {}

  // ILogger接口要求的基础方法
  info(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.unifiedLogger.info(message, { ...context, component: this.componentName });
  }

  warn(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.unifiedLogger.warn(message, { ...context, component: this.componentName });
  }

  error(message: string, ...meta: any[]): void {
    const error = meta.length > 0 ? meta[0] : undefined;
    const context = meta.length > 1 ? meta[1] : undefined;
    this.unifiedLogger.error(message, error, { ...context, component: this.componentName });
  }

  debug(message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.unifiedLogger.debug(message, { ...context, component: this.componentName });
  }

  log(level: string, message: string, ...meta: any[]): void {
    const context = meta.length > 0 ? meta[0] : undefined;
    this.unifiedLogger.log(level, message, { ...context, component: this.componentName });
  }

  // ILogger接口要求的属性和方法
  get silent(): boolean {
    return this.unifiedLogger.silent;
  }

  isLevelEnabled(level: string): boolean {
    return this.unifiedLogger.isLevelEnabled(level);
  }

  isDebugEnabled(): boolean {
    return this.unifiedLogger.isDebugEnabled();
  }

  isInfoEnabled(): boolean {
    return this.unifiedLogger.isInfoEnabled();
  }

  isWarnEnabled(): boolean {
    return this.unifiedLogger.isWarnEnabled();
  }

  isErrorEnabled(): boolean {
    return this.unifiedLogger.isErrorEnabled();
  }

  child(options?: object): ILogger {
    return this.unifiedLogger.child({ ...options, component: this.componentName });
  }

  // 扩展方法（保持向后兼容）
  performance(message: string, duration: number, options: PerformanceLogOptions, context?: LogContext): void {
    if (isUnifiedLogger(this.unifiedLogger)) {
      this.unifiedLogger.performance(message, duration, options, { ...context, component: this.componentName });
    } else {
      this.info(`Performance: ${message} (${duration}ms)`, { ...context, component: this.componentName });
    }
  }

  startOperation(operation: string, context?: LogContext): OperationTimer {
    if (isUnifiedLogger(this.unifiedLogger)) {
      return this.unifiedLogger.startOperation(operation, { ...context, component: this.componentName });
    } else {
      return new OperationTimer(this.unifiedLogger as any, operation, { ...context, component: this.componentName });
    }
  }
}

/**
 * 操作计时器
 * 用于测量和记录操作执行时间
 */
export class OperationTimer {
  private readonly startTime: number;

  constructor(
    private readonly logger: ILogger,
    private readonly operation: string,
    private readonly context?: LogContext
  ) {
    this.startTime = Date.now();
  }

  /**
   * 结束操作并记录日志
   */
  end(message?: string, additionalContext?: LogContext): void {
    const duration = Date.now() - this.startTime;
    const finalMessage = message || `Operation ${this.operation} completed`;
    const finalContext = { ...this.context, ...additionalContext };

    if (isUnifiedLogger(this.logger)) {
      this.logger.performance(finalMessage, duration, {
        operation: this.operation,
        threshold: 1000 // 1秒阈值
      }, finalContext);
    } else {
      this.logger.info(`Performance: ${finalMessage} (${duration}ms)`, finalContext);
    }
  }

  /**
   * 结束操作并记录错误
   */
  endWithError(error: Error | unknown, message?: string, additionalContext?: LogContext): void {
    const duration = Date.now() - this.startTime;
    const finalMessage = message || `Operation ${this.operation} failed`;
    const finalContext = { ...this.context, ...additionalContext };

    this.logger.error(finalMessage, error, finalContext);
  }
}
