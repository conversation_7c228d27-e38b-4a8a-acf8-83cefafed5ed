/**
 * 适配器工厂测试
 * 验证适配器工厂的创建和管理功能
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { AdapterFactory } from '../adapter-factory';
import { ExternalDataAdapterBase } from '../external-data-adapter-base';
import { AdapterConfig } from '../../interfaces/IExternalDataAdapter';
import { TYPES } from '../../../di/types/index';

// 测试适配器类
class TestAdapterA extends ExternalDataAdapterBase {
  readonly adapterName = 'test-adapter-a';
  readonly sourceName = 'test-source-a';
}

class TestAdapterB extends ExternalDataAdapterBase {
  readonly adapterName = 'test-adapter-b';
  readonly sourceName = 'test-source-b';
}

describe('AdapterFactory', () => {
  let container: Container;
  let factory: AdapterFactory;
  let mockConfig: AdapterConfig;

  beforeEach(() => {
    container = new Container();
    
    // 模拟配置
    mockConfig = {
      name: 'test-adapter',
      baseUrl: 'https://api.test.com',
      timeout: 5000,
      maxRetries: 2,
      enableCache: true
    };

    // 模拟依赖服务
    const mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    };

    const mockErrorHandler = {
      handleError: jest.fn((error) => error)
    };

    const mockPipeline = {
      process: jest.fn().mockResolvedValue({
        data: { processed: true },
        metadata: { processedAt: new Date() },
        qualityMetrics: { overallQuality: 0.9 },
        processingTrace: []
      }),
      processBatch: jest.fn(),
      getHealthStatus: jest.fn(),
      getStatistics: jest.fn()
    };

    // 绑定模拟服务
    container.bind(TYPES.Logger).toConstantValue(mockLogger);
    container.bind(TYPES.Shared.UnifiedErrorHandler).toConstantValue(mockErrorHandler);
    container.bind(TYPES.DataProcessing.DataProcessingPipeline).toConstantValue(mockPipeline);

    // 创建工厂实例
    factory = new AdapterFactory(mockPipeline, mockLogger, mockErrorHandler);
  });

  afterEach(() => {
    container.unbindAll();
  });

  describe('适配器注册', () => {
    it('应该能够注册适配器', () => {
      factory.registerAdapter('test-a', TestAdapterA, {
        description: '测试适配器A',
        supportedDataTypes: ['price', 'kline'],
        requiredConfig: ['name', 'baseUrl']
      });

      expect(factory.isAdapterRegistered('test-a')).toBe(true);
      expect(factory.getSupportedAdapters()).toContain('test-a');
    });

    it('应该能够注销适配器', () => {
      factory.registerAdapter('test-a', TestAdapterA);
      expect(factory.isAdapterRegistered('test-a')).toBe(true);

      const result = factory.unregisterAdapter('test-a');
      expect(result).toBe(true);
      expect(factory.isAdapterRegistered('test-a')).toBe(false);
    });

    it('应该正确处理注销不存在的适配器', () => {
      const result = factory.unregisterAdapter('non-existent');
      expect(result).toBe(false);
    });
  });

  describe('适配器创建', () => {
    beforeEach(() => {
      factory.registerAdapter('test-a', TestAdapterA, {
        supportedDataTypes: ['price'],
        requiredConfig: ['name', 'baseUrl']
      });
    });

    it('应该能够创建适配器实例', () => {
      const adapter = factory.createAdapter('test-a', mockConfig);
      
      expect(adapter).toBeInstanceOf(TestAdapterA);
      expect(adapter.adapterName).toBe('test-adapter-a');
    });

    it('应该在创建未注册的适配器时抛出错误', () => {
      expect(() => {
        factory.createAdapter('non-existent', mockConfig);
      }).toThrow('未找到适配器: non-existent');
    });

    it('应该验证配置的必需字段', () => {
      const invalidConfig = { name: 'test' }; // 缺少baseUrl
      
      expect(() => {
        factory.createAdapter('test-a', invalidConfig as AdapterConfig);
      }).toThrow('缺少必需配置字段: baseUrl');
    });

    it('应该验证baseUrl格式', () => {
      const invalidConfig = {
        ...mockConfig,
        baseUrl: 'invalid-url'
      };
      
      expect(() => {
        factory.createAdapter('test-a', invalidConfig);
      }).toThrow('无效的baseUrl格式');
    });

    it('应该验证timeout值', () => {
      const invalidConfig = {
        ...mockConfig,
        timeout: -1
      };
      
      expect(() => {
        factory.createAdapter('test-a', invalidConfig);
      }).toThrow('无效的timeout值');
    });
  });

  describe('批量操作', () => {
    beforeEach(() => {
      factory.registerAdapter('test-a', TestAdapterA);
      factory.registerAdapter('test-b', TestAdapterB);
    });

    it('应该能够批量创建适配器', () => {
      const configs = [
        { name: 'test-a', config: { ...mockConfig, name: 'adapter-a' } },
        { name: 'test-b', config: { ...mockConfig, name: 'adapter-b' } }
      ];

      const adapters = factory.createAdapters(configs);
      
      expect(adapters.size).toBe(2);
      expect(adapters.has('test-a')).toBe(true);
      expect(adapters.has('test-b')).toBe(true);
    });

    it('应该处理批量创建中的部分失败', () => {
      const configs = [
        { name: 'test-a', config: mockConfig },
        { name: 'non-existent', config: mockConfig }
      ];

      const adapters = factory.createAdapters(configs);
      
      expect(adapters.size).toBe(1);
      expect(adapters.has('test-a')).toBe(true);
      expect(adapters.has('non-existent')).toBe(false);
    });
  });

  describe('数据类型支持', () => {
    beforeEach(() => {
      factory.registerAdapter('test-a', TestAdapterA, {
        supportedDataTypes: ['price', 'kline']
      });
      factory.registerAdapter('test-b', TestAdapterB, {
        supportedDataTypes: ['trade', 'orderbook']
      });
    });

    it('应该能够根据数据类型获取适配器', () => {
      const priceAdapters = factory.getAdaptersByDataType('price');
      const tradeAdapters = factory.getAdaptersByDataType('trade');
      
      expect(priceAdapters).toContain('test-a');
      expect(priceAdapters).not.toContain('test-b');
      expect(tradeAdapters).toContain('test-b');
      expect(tradeAdapters).not.toContain('test-a');
    });

    it('应该处理不支持的数据类型', () => {
      const adapters = factory.getAdaptersByDataType('unsupported');
      expect(adapters).toEqual([]);
    });
  });

  describe('注册信息管理', () => {
    it('应该能够获取适配器注册信息', () => {
      factory.registerAdapter('test-a', TestAdapterA, {
        description: '测试适配器A',
        supportedDataTypes: ['price']
      });

      const registration = factory.getAdapterRegistration('test-a');
      
      expect(registration).toBeDefined();
      expect(registration?.name).toBe('test-a');
      expect(registration?.description).toBe('测试适配器A');
      expect(registration?.supportedDataTypes).toContain('price');
    });

    it('应该能够获取所有注册信息', () => {
      factory.registerAdapter('test-a', TestAdapterA);
      factory.registerAdapter('test-b', TestAdapterB);

      const registrations = factory.getAllRegistrations();
      
      expect(registrations).toHaveLength(2);
      expect(registrations.map(r => r.name)).toContain('test-a');
      expect(registrations.map(r => r.name)).toContain('test-b');
    });
  });

  describe('工厂统计', () => {
    it('应该提供正确的统计信息', () => {
      factory.registerAdapter('test-a', TestAdapterA, {
        supportedDataTypes: ['price', 'kline']
      });
      factory.registerAdapter('test-b', TestAdapterB, {
        supportedDataTypes: ['trade']
      });

      const stats = factory.getFactoryStatistics();
      
      expect(stats.registeredAdapters).toBe(2);
      expect(stats.adapterNames).toContain('test-a');
      expect(stats.adapterNames).toContain('test-b');
      expect(stats.dataTypeSupport.price).toContain('test-a');
      expect(stats.dataTypeSupport.trade).toContain('test-b');
    });
  });

  describe('配置验证', () => {
    beforeEach(() => {
      factory.registerAdapter('test-a', TestAdapterA, {
        requiredConfig: ['name', 'baseUrl', 'apiKey'],
        optionalConfig: ['timeout', 'maxRetries']
      });
    });

    it('应该验证速率限制配置', () => {
      const invalidConfig = {
        ...mockConfig,
        rateLimitConfig: {
          requestsPerSecond: -1,
          burstSize: 10
        }
      };
      
      expect(() => {
        factory.createAdapter('test-a', invalidConfig);
      }).toThrow('无效的requestsPerSecond值');
    });

    it('应该验证重试次数配置', () => {
      const invalidConfig = {
        ...mockConfig,
        maxRetries: 15
      };
      
      expect(() => {
        factory.createAdapter('test-a', invalidConfig);
      }).toThrow('无效的maxRetries值');
    });
  });
});
