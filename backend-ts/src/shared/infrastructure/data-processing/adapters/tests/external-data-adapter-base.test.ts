/**
 * 外部数据适配器基类测试
 * 验证基类的核心功能和接口实现
 */

import 'reflect-metadata';
import { Container } from 'inversify';
import { ExternalDataAdapterBase } from '../external-data-adapter-base';
import { AdapterConfig, AdapterErrorType } from '../../interfaces/IExternalDataAdapter';
import { ProcessingContext } from '../../interfaces/IDataProcessingPipeline';
import { TYPES } from '../../../di/types/index';

// 测试适配器实现
class TestAdapter extends ExternalDataAdapterBase {
  readonly adapterName = 'test-adapter';
  readonly sourceName = 'test-source';

  // 公开受保护的方法用于测试
  public testGenerateCacheKey(endpoint: string, params?: any): string {
    return this.generateCacheKey(endpoint, params);
  }

  public testHandleError(error: any, endpoint: string, context: ProcessingContext) {
    return this.handleError(error, endpoint, context);
  }
}

describe('ExternalDataAdapterBase', () => {
  let container: Container;
  let adapter: TestAdapter;
  let mockConfig: AdapterConfig;

  beforeEach(() => {
    container = new Container();
    
    // 模拟配置
    mockConfig = {
      name: 'test-adapter',
      baseUrl: 'https://api.test.com',
      timeout: 5000,
      maxRetries: 2,
      rateLimitConfig: {
        requestsPerSecond: 5,
        burstSize: 10
      },
      enableCache: true,
      cacheValidityMinutes: 5
    };

    // 模拟依赖服务
    const mockLogger = {
      info: vi.fn(),
      debug: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    };

    const mockErrorHandler = {
      handleError: jest.fn((error) => error)
    };

    const mockPipeline = {
      process: jest.fn().mockResolvedValue({
        data: { processed: true },
        metadata: { processedAt: new Date() },
        qualityMetrics: { overallQuality: 0.9 },
        processingTrace: []
      }),
      processBatch: jest.fn(),
      getHealthStatus: jest.fn(),
      getStatistics: jest.fn()
    };

    // 绑定模拟服务
    container.bind(TYPES.Logger).toConstantValue(mockLogger);
    container.bind(TYPES.Shared.UnifiedErrorHandler).toConstantValue(mockErrorHandler);
    container.bind(TYPES.DataProcessing.DataProcessingPipeline).toConstantValue(mockPipeline);

    // 创建适配器实例
    adapter = new TestAdapter(
      mockConfig,
      mockPipeline,
      mockLogger,
      mockErrorHandler
    );
  });

  afterEach(() => {
    container.unbindAll();
  });

  describe('基本属性', () => {
    it('应该正确设置适配器名称和数据源', () => {
      expect(adapter.adapterName).toBe('test-adapter');
      expect(adapter.sourceName).toBe('test-source');
    });

    it('应该正确初始化配置', () => {
      const config = adapter.getConfig();
      expect(config.name).toBe('test-adapter');
      expect(config.baseUrl).toBe('https://api.test.com');
      expect(config.timeout).toBe(5000);
    });
  });

  describe('配置管理', () => {
    it('应该能够更新配置', () => {
      const newConfig = { timeout: 10000, maxRetries: 5 };
      adapter.updateConfig(newConfig);
      
      const updatedConfig = adapter.getConfig();
      expect(updatedConfig.timeout).toBe(10000);
      expect(updatedConfig.maxRetries).toBe(5);
    });

    it('应该保持其他配置不变', () => {
      adapter.updateConfig({ timeout: 8000 });
      
      const config = adapter.getConfig();
      expect(config.name).toBe('test-adapter');
      expect(config.baseUrl).toBe('https://api.test.com');
      expect(config.timeout).toBe(8000);
    });
  });

  describe('缓存管理', () => {
    it('应该能够设置和获取缓存', () => {
      const testData = { test: 'data' };
      adapter.setCache('test-key', testData, 10);
      
      const cachedData = adapter.getCache('test-key');
      expect(cachedData).toEqual(testData);
    });

    it('应该正确处理缓存过期', (done) => {
      const testData = { test: 'data' };
      adapter.setCache('test-key', testData, 0.01); // 0.6秒过期
      
      setTimeout(() => {
        const cachedData = adapter.getCache('test-key');
        expect(cachedData).toBeNull();
        done();
      }, 100);
    });

    it('应该能够检查缓存是否存在', () => {
      adapter.setCache('test-key', { test: 'data' });
      
      expect(adapter.hasCache('test-key')).toBe(true);
      expect(adapter.hasCache('non-existent-key')).toBe(false);
    });

    it('应该能够删除特定缓存', () => {
      adapter.setCache('test-key', { test: 'data' });
      expect(adapter.hasCache('test-key')).toBe(true);
      
      adapter.deleteCache('test-key');
      expect(adapter.hasCache('test-key')).toBe(false);
    });

    it('应该能够清除所有缓存', () => {
      adapter.setCache('key1', { data: 1 });
      adapter.setCache('key2', { data: 2 });
      
      adapter.clearCache();
      
      expect(adapter.hasCache('key1')).toBe(false);
      expect(adapter.hasCache('key2')).toBe(false);
    });
  });

  describe('性能监控', () => {
    it('应该记录请求指标', () => {
      adapter.recordRequestMetrics(true, 100, '/test-endpoint');
      adapter.recordRequestMetrics(false, 200, '/test-endpoint');
      
      const metrics = adapter.getPerformanceMetrics();
      expect(metrics.averageLatency).toBe(150);
      expect(metrics.successRate).toBe(0.5);
      expect(metrics.errorRate).toBe(0.5);
    });

    it('应该计算每分钟请求数', () => {
      // 记录一些请求
      for (let i = 0; i < 5; i++) {
        adapter.recordRequestMetrics(true, 100, '/test');
      }
      
      const metrics = adapter.getPerformanceMetrics();
      expect(metrics.requestsPerMinute).toBe(5);
    });

    it('应该能够重置指标', () => {
      adapter.recordRequestMetrics(true, 100, '/test');
      adapter.resetMetrics();
      
      const metrics = adapter.getPerformanceMetrics();
      expect(metrics.averageLatency).toBe(0);
      expect(metrics.successRate).toBe(1);
    });
  });

  describe('统计信息', () => {
    it('应该正确初始化统计信息', async () => {
      const stats = await adapter.getStatistics();
      
      expect(stats.totalRequests).toBe(0);
      expect(stats.successfulRequests).toBe(0);
      expect(stats.failedRequests).toBe(0);
      expect(stats.averageLatency).toBe(0);
    });
  });

  describe('健康检查', () => {
    it('应该返回健康状态', async () => {
      // 模拟testConnection返回true
      jest.spyOn(adapter, 'testConnection').mockResolvedValue(true);
      
      const health = await adapter.healthCheck();
      
      expect(health.isHealthy).toBe(true);
      expect(health.lastCheck).toBeInstanceOf(Date);
      expect(typeof health.latency).toBe('number');
    });

    it('应该处理健康检查失败', async () => {
      // 模拟testConnection抛出错误
      jest.spyOn(adapter, 'testConnection').mockRejectedValue(new Error('连接失败'));
      
      const health = await adapter.healthCheck();
      
      expect(health.isHealthy).toBe(false);
      expect(health.error).toBe('连接失败');
    });
  });

  describe('缓存键生成', () => {
    it('应该生成唯一的缓存键', () => {
      const key1 = adapter.testGenerateCacheKey('/endpoint1', { param: 'value1' });
      const key2 = adapter.testGenerateCacheKey('/endpoint2', { param: 'value2' });
      const key3 = adapter.testGenerateCacheKey('/endpoint1', { param: 'value1' });
      
      expect(key1).not.toBe(key2);
      expect(key1).toBe(key3); // 相同参数应该生成相同键
    });

    it('应该处理无参数的情况', () => {
      const key = adapter.testGenerateCacheKey('/endpoint');
      expect(key).toContain('test-adapter:/endpoint:');
    });
  });

  describe('错误处理', () => {
    it('应该正确分类超时错误', () => {
      const timeoutError = { code: 'ECONNABORTED', message: 'timeout' };
      const context: ProcessingContext = {
        source: 'test',
        dataType: 'test',
        businessSystem: 'test'
      };
      
      const adapterError = adapter.testHandleError(timeoutError, '/test', context);
      expect(adapterError.type).toBe(AdapterErrorType.TIMEOUT_ERROR);
    });

    it('应该正确分类速率限制错误', () => {
      const rateLimitError = { response: { status: 429 } };
      const context: ProcessingContext = {
        source: 'test',
        dataType: 'test',
        businessSystem: 'test'
      };
      
      const adapterError = adapter.testHandleError(rateLimitError, '/test', context);
      expect(adapterError.type).toBe(AdapterErrorType.RATE_LIMIT_ERROR);
    });

    it('应该正确分类网络错误', () => {
      const networkError = { code: 'ENOTFOUND' };
      const context: ProcessingContext = {
        source: 'test',
        dataType: 'test',
        businessSystem: 'test'
      };
      
      const adapterError = adapter.testHandleError(networkError, '/test', context);
      expect(adapterError.type).toBe(AdapterErrorType.NETWORK_ERROR);
    });
  });

  describe('fetchAndProcess', () => {
    it('应该成功获取和处理数据', async () => {
      // 模拟HTTP请求
      const mockHttpResponse = { data: { test: 'data' } };
      jest.spyOn(adapter['httpClient'], 'get').mockResolvedValue(mockHttpResponse);

      const result = await adapter.fetchAndProcess(
        '/test-endpoint',
        { params: { test: 'param' } },
        { dataType: 'test' }
      );

      expect(result).toEqual({ processed: true });
      expect(adapter['httpClient'].get).toHaveBeenCalledWith('/test-endpoint', {
        timeout: 5000,
        params: { test: 'param' }
      });
    });

    it('应该使用缓存数据', async () => {
      // 设置缓存
      adapter.setCache('test-key', { cached: 'data' });

      const result = await adapter.fetchAndProcess(
        '/test-endpoint',
        { useCache: true, cacheKey: 'test-key' },
        { dataType: 'test' }
      );

      expect(result).toEqual({ cached: 'data' });
    });

    it('应该处理HTTP请求错误', async () => {
      // 模拟HTTP错误
      const httpError = new Error('Network error');
      jest.spyOn(adapter['httpClient'], 'get').mockRejectedValue(httpError);

      await expect(adapter.fetchAndProcess(
        '/test-endpoint',
        {},
        { dataType: 'test' }
      )).rejects.toThrow();
    });
  });

  describe('批量处理', () => {
    it('应该能够处理批量请求', async () => {
      // 模拟fetchAndProcess方法
      jest.spyOn(adapter, 'fetchAndProcess').mockResolvedValue({ data: 'test' });

      const requests = [
        {
          endpoint: '/test1',
          options: {},
          context: { dataType: 'test' }
        },
        {
          endpoint: '/test2',
          options: {},
          context: { dataType: 'test' }
        }
      ];

      const results = await adapter.fetchAndProcessBatch(requests);

      expect(results).toHaveLength(2);
      expect(adapter.fetchAndProcess).toHaveBeenCalledTimes(2);
    });

    it('应该处理批量请求中的部分失败', async () => {
      // 模拟部分成功部分失败
      jest.spyOn(adapter, 'fetchAndProcess')
        .mockResolvedValueOnce({ data: 'success' })
        .mockRejectedValueOnce(new Error('Failed'));

      const requests = [
        { endpoint: '/test1', options: {}, context: { dataType: 'test' } },
        { endpoint: '/test2', options: {}, context: { dataType: 'test' } }
      ];

      await expect(adapter.fetchAndProcessBatch(requests)).rejects.toThrow('Failed');
    });
  });

  describe('连接测试', () => {
    it('应该成功测试连接', async () => {
      // 模拟成功的HTTP请求
      jest.spyOn(adapter['httpClient'], 'get').mockResolvedValue({ data: 'ok' });

      const result = await adapter.testConnection();
      expect(result).toBe(true);
    });

    it('应该处理连接失败', async () => {
      // 模拟失败的HTTP请求
      jest.spyOn(adapter['httpClient'], 'get').mockRejectedValue(new Error('Connection failed'));

      const result = await adapter.testConnection();
      expect(result).toBe(false);
    });
  });
});
