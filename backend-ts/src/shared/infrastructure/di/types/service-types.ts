/**
 * 服务层类型定义
 * 拆分自types.ts，减少模块耦合度
 */

/**
 * 应用服务类型
 */
export const SERVICE_TYPES = {
  ApplicationService: 'ApplicationService',
  DomainService: 'DomainService',
} as const;

/**
 * 共享服务类型
 */
export const SHARED_SERVICE_TYPES = {
  // 分析服务
  DynamicWeightingService: Symbol.for('Shared.DynamicWeightingService'),
  PatternRecognitionService: Symbol.for('Shared.PatternRecognitionService'),
  MultiTimeframeService: Symbol.for('Shared.MultiTimeframeService'),
  FinancialMetricsService: Symbol.for('Shared.FinancialMetricsService'),
  UnifiedTechnicalIndicatorCalculator: Symbol.for('Shared.UnifiedTechnicalIndicatorCalculator'),
  ConfidenceCalculator: Symbol.for('Shared.ConfidenceCalculator'),

  // 配置服务
  RiskConfigService: Symbol.for('Shared.RiskConfigService'),

  // API监控服务已移除，改为手动执行模式
  // AutomatedAPIMonitor: Symbol.for('Shared.AutomatedAPIMonitor'),
  // APIDiscoveryService: Symbol.for('Shared.APIDiscoveryService'),
  // StartupAPIMonitor: Symbol.for('Shared.StartupAPIMonitor'),
  
  // 数据服务
  OptimizedQueryManager: Symbol.for('Shared.OptimizedQueryManager'),
  RepositoryBaseService: Symbol.for('Shared.RepositoryBaseService'),
  DataMappingService: Symbol.for('Shared.DataMappingService'),
  UnifiedDataMapper: Symbol.for('Shared.UnifiedDataMapper'),
  UnifiedMarketDataProcessor: Symbol.for('Shared.UnifiedMarketDataProcessor'),
  
  // 错误处理
  UnifiedErrorHandler: Symbol.for('Shared.UnifiedErrorHandler'),
  
  // 缓存服务
  CacheService: Symbol.for('Shared.CacheService'),
  MultiTierCacheService: Symbol.for('Shared.MultiTierCacheService'),
  CacheConsistencyManager: Symbol.for('Shared.CacheConsistencyManager'),
  CachePerformanceMonitor: Symbol.for('Shared.CachePerformanceMonitor'),
  
  // 数据质量监控服务
  EnhancedDataQualityMonitor: Symbol.for('Shared.EnhancedDataQualityMonitor'),
  DataQualityDashboard: Symbol.for('Shared.DataQualityDashboard'),

  // 风险强制执行服务
  RiskEnforcementEngine: Symbol.for('Shared.RiskEnforcementEngine'),
  RealTimeRiskMonitor: Symbol.for('Shared.RealTimeRiskMonitor'),

  // 订单生命周期管理服务
  OrderLifecycleManager: Symbol.for('Shared.OrderLifecycleManager'),
  OrderStatusSyncService: Symbol.for('Shared.OrderStatusSyncService'),

  // 交易执行监控服务
  EnhancedTradingExecutionMonitor: Symbol.for('Shared.EnhancedTradingExecutionMonitor'),

  // 验证服务
  ValidationService: Symbol.for('Shared.ValidationService'),
  
  // 加密服务
  EncryptionService: Symbol.for('Shared.EncryptionService'),
  
  // 通知服务
  NotificationService: Symbol.for('Shared.NotificationService'),
  
  // 文件服务
  FileService: Symbol.for('Shared.FileService'),
  
  // 邮件服务
  EmailService: Symbol.for('Shared.EmailService'),
  
  // 短信服务
  SMSService: Symbol.for('Shared.SMSService'),
  
  // 推送服务
  PushNotificationService: Symbol.for('Shared.PushNotificationService'),
  
  // 统一分析服务管理器
  UnifiedAnalysisServiceManager: Symbol.for('Shared.UnifiedAnalysisServiceManager'),
  
  // 统一AI服务管理器
  UnifiedAIServiceManager: Symbol.for('Shared.UnifiedAIServiceManager'),

  // AI调用日志服务
  AICallLogService: Symbol.for('Shared.AICallLogService'),
  
  // 监控服务配置函数
  ConfigureMonitoringServices: Symbol.for('Shared.ConfigureMonitoringServices'),

  // 嵌入提供者
  OpenAIEmbeddingProvider: Symbol.for('Shared.OpenAIEmbeddingProvider'),
  CohereEmbeddingProvider: Symbol.for('Shared.CohereEmbeddingProvider'),
  LocalEmbeddingProvider: Symbol.for('Shared.LocalEmbeddingProvider'),
  EnhancedVectorService: Symbol.for('Shared.EnhancedVectorService'),
  VectorDatabaseService: Symbol.for('Shared.VectorDatabaseService'),
  IntelligentCacheManager: Symbol.for('Shared.IntelligentCacheManager'),
  SemanticCacheEngine: Symbol.for('Shared.SemanticCacheEngine'),
  EnhancedSemanticCacheEngine: Symbol.for('Shared.EnhancedSemanticCacheEngine'),

  // AI服务
  DynamicCacheStrategy: Symbol.for('Shared.DynamicCacheStrategy'),
  IntelligentAIScheduler: Symbol.for('Shared.IntelligentAIScheduler'),
  RequestMerger: Symbol.for('Shared.RequestMerger'),
  OptimizedIntegratedAIService: Symbol.for('Shared.OptimizedIntegratedAIService'),
  OptimizedRealtimePushService: Symbol.for('Shared.OptimizedRealtimePushService'),
  AIRealtimeService: Symbol.for('Shared.AIRealtimeService'),
  UnifiedSimilarityService: Symbol.for('Shared.UnifiedSimilarityService'),

  // 配置管理
  UnifiedConfigManager: Symbol.for('Shared.UnifiedConfigManager'),
  UnifiedPerformanceConfigManager: Symbol.for('Shared.UnifiedPerformanceConfigManager'),
  UnifiedEnvironmentManager: Symbol.for('Shared.UnifiedEnvironmentManager'),
  UnifiedMonitoringManager: Symbol.for('Shared.UnifiedMonitoringManager'),
  UnifiedHealthService: Symbol.for('Shared.UnifiedHealthService'),
  
  // 系统服务
  SystemAutoRepair: Symbol.for('Shared.SystemAutoRepair'),
  EnhancedCacheManager: Symbol.for('Shared.EnhancedCacheManager'),
  ConcurrencyOptimizer: Symbol.for('Shared.ConcurrencyOptimizer'),
  UnifiedAlertSystem: Symbol.for('Shared.UnifiedAlertSystem'),
  LogAggregator: Symbol.for('Shared.LogAggregator'),
  UnifiedPerformanceManager: Symbol.for('Shared.UnifiedPerformanceManager'),
  QueryManager: Symbol.for('Shared.QueryManager'),
  UserIdentityService: Symbol.for('Shared.UserIdentityService'),

  // DTO映射器
  PriceDataDtoMapper: Symbol.for('Shared.PriceDataDtoMapper'),
  KlineDataDtoMapper: Symbol.for('Shared.KlineDataDtoMapper'),
  TradingPositionDtoMapper: Symbol.for('Shared.TradingPositionDtoMapper'),
  MarketDataDtoMapperFactory: Symbol.for('Shared.MarketDataDtoMapperFactory'),
  TradingExecutionDtoMapperFactory: Symbol.for('Shared.TradingExecutionDtoMapperFactory'),
  UnifiedDtoMapperRegistry: Symbol.for('Shared.UnifiedDtoMapperRegistry'),

  // 健康检查
  HealthCheckAggregator: Symbol.for('Shared.HealthCheckAggregator'),
  HealthCheckController: Symbol.for('Shared.HealthCheckController'),
  AIServiceHealthProvider: Symbol.for('Shared.AIServiceHealthProvider'),
  ExternalApiHealthProvider: Symbol.for('Shared.ExternalApiHealthProvider'),
  TradingMonitoringHealthProvider: Symbol.for('Shared.TradingMonitoringHealthProvider'),
  DatabaseHealthProvider: Symbol.for('Shared.DatabaseHealthProvider'),
  WebSocketHealthProvider: Symbol.for('Shared.WebSocketHealthProvider'),

  // 中间件
  EnhancedAuthMiddleware: Symbol.for('Shared.EnhancedAuthMiddleware'),

  // 统一数据同步协调器
  UnifiedDataSyncCoordinator: Symbol.for('Services.UnifiedDataSyncCoordinator'),

  // Express中间件
  ExpressMiddleware: Symbol.for('Shared.ExpressMiddleware'),
} as const;
