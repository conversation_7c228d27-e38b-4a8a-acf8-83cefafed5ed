/**
 * 仓储层类型定义
 * 拆分自types.ts，减少模块耦合度
 */

/**
 * 用户管理仓储类型
 */
export const USER_MANAGEMENT_REPOSITORY_TYPES = {
  UserRepository: Symbol.for('UserManagement.UserRepository'),
  SecurityEventRepository: Symbol.for('UserManagement.SecurityEventRepository'),
  ApiKeyRepository: Symbol.for('UserManagement.ApiKeyRepository'),
  AuditLogRepository: Symbol.for('UserManagement.AuditLogRepository'),
  InvitationRepository: Symbol.for('UserManagement.InvitationRepository'),
} as const;

/**
 * 市场数据仓储类型
 */
export const MARKET_DATA_REPOSITORY_TYPES = {
  MarketDataRepository: Symbol.for('MarketData.MarketDataRepository'),
  PriceDataRepository: Symbol.for('MarketData.PriceDataRepository'),
  VolumeDataRepository: Symbol.for('MarketData.VolumeDataRepository'),
  NewsDataRepository: Symbol.for('MarketData.NewsDataRepository'),
  EconomicDataRepository: Symbol.for('MarketData.EconomicDataRepository'),
  HistoricalDataRepository: Symbol.for('MarketData.HistoricalDataRepository'),
  SymbolRepository: Symbol.for('MarketData.SymbolRepository'),
} as const;

/**
 * 风险管理仓储类型
 */
export const RISK_MANAGEMENT_REPOSITORY_TYPES = {
  RiskAssessmentRepository: Symbol.for('RiskManagement.RiskAssessmentRepository'),
  AlertRepository: Symbol.for('RiskManagement.AlertRepository'),
} as const;

/**
 * 趋势分析仓储类型
 */
export const TREND_ANALYSIS_REPOSITORY_TYPES = {
  TrendDataRepository: Symbol.for('TrendAnalysis.TrendDataRepository'),
  AnalysisResultRepository: Symbol.for('TrendAnalysis.AnalysisResultRepository'),
} as const;

/**
 * 用户配置仓储类型
 */
export const USER_CONFIG_REPOSITORY_TYPES = {
  UserConfigRepository: Symbol.for('UserConfig.UserConfigRepository'),
  UserPreferencesRepository: Symbol.for('UserConfig.UserPreferencesRepository'),
  UserProfileRepository: Symbol.for('UserConfig.UserProfileRepository'),
  UserLLMConfigRepository: Symbol.for('UserConfig.UserLLMConfigRepository'),
  UserModelPreferenceRepository: Symbol.for('UserConfig.UserModelPreferenceRepository'),
} as const;

/**
 * 交易执行仓储类型
 */
export const TRADING_EXECUTION_REPOSITORY_TYPES = {
  OrderRepository: Symbol.for('TradingExecution.OrderRepository'),
  TradeRepository: Symbol.for('TradingExecution.TradeRepository'),
  PositionRepository: Symbol.for('TradingExecution.PositionRepository'),
  PortfolioRepository: Symbol.for('TradingExecution.PortfolioRepository'),
} as const;

/**
 * 交易执行服务类型
 */
export const TRADING_EXECUTION_SERVICE_TYPES = {
  // 应用服务
  TradingExecutionApplicationService: Symbol.for('TradingExecution.TradingExecutionApplicationService'),

  // 执行引擎
  ExecutionEngineRouter: Symbol.for('TradingExecution.ExecutionEngineRouter'),
  SimulationEngine: Symbol.for('TradingExecution.SimulationEngine'),
  BinanceEngine: Symbol.for('TradingExecution.BinanceEngine'),

  // API客户端
  BinanceApiClient: Symbol.for('TradingExecution.BinanceApiClient'),
  CredentialManager: Symbol.for('TradingExecution.CredentialManager'),
  BinanceWebSocketService: Symbol.for('TradingExecution.BinanceWebSocketService'),
  OrderIdManager: Symbol.for('TradingExecution.OrderIdManager'),

  // 安全和风控
  SecurityManager: Symbol.for('TradingExecution.SecurityManager'),
  TradingLimitController: Symbol.for('TradingExecution.TradingLimitController'),
  EmergencyStopManager: Symbol.for('TradingExecution.EmergencyStopManager'),

  // 策略同步
  StrategySyncService: Symbol.for('TradingExecution.StrategySyncService'),

  // 双轨学习
  DualEnvironmentLearningService: Symbol.for('TradingExecution.DualEnvironmentLearningService'),

  // 双轨监控
  DualTrackMonitoringService: Symbol.for('TradingExecution.DualTrackMonitoringService'),
  WebhookAlertService: Symbol.for('TradingExecution.WebhookAlertService'),

  // 执行引擎
  RealSlippageCalculator: Symbol.for('TradingExecution.RealSlippageCalculator'),
  RealOrderExecutionEngine: Symbol.for('TradingExecution.RealOrderExecutionEngine'),
  TradingStrategyEngine: Symbol.for('TradingExecution.TradingStrategyEngine'),
  PositionManager: Symbol.for('TradingExecution.PositionManager'),
  OrderExecutor: Symbol.for('TradingExecution.OrderExecutor'),
  RiskMonitor: Symbol.for('TradingExecution.RiskMonitor'),
} as const;

/**
 * 数据处理仓储类型
 */
export const DATA_PROCESSING_REPOSITORY_TYPES = {
  DataProcessingJobRepository: Symbol.for('DataProcessing.DataProcessingJobRepository'),
  ProcessedDataRepository: Symbol.for('DataProcessing.ProcessedDataRepository'),
} as const;

/**
 * 数据处理服务类型
 */
export const DATA_PROCESSING_SERVICE_TYPES = {
  DataProcessingPipeline: Symbol.for('DataProcessing.DataProcessingPipeline'),
  PipelineStageCoordinator: Symbol.for('DataProcessing.PipelineStageCoordinator'),
  StageExecutorFactory: Symbol.for('DataProcessing.StageExecutorFactory'),
  AdapterFactory: Symbol.for('DataProcessing.AdapterFactory'),
  StrategyFactory: Symbol.for('DataProcessing.StrategyFactory'),
  StrategyMonitor: Symbol.for('DataProcessing.StrategyMonitor'),
  StrategyManager: Symbol.for('DataProcessing.StrategyManager'),

  // 执行器
  ReceiveStageExecutor: Symbol.for('DataProcessing.ReceiveStageExecutor'),
  TimestampStageExecutor: Symbol.for('DataProcessing.TimestampStageExecutor'),
  ValidationStageExecutor: Symbol.for('DataProcessing.ValidationStageExecutor'),
  CleaningStageExecutor: Symbol.for('DataProcessing.CleaningStageExecutor'),
  ProcessingStageExecutor: Symbol.for('DataProcessing.ProcessingStageExecutor'),
  MappingStageExecutor: Symbol.for('DataProcessing.MappingStageExecutor'),
  SyncStageExecutor: Symbol.for('DataProcessing.SyncStageExecutor'),

  // 情感分析
  SentimentDataFetchingStage: Symbol.for('DataProcessing.SentimentDataFetchingStage'),
  SentimentCalculationStage: Symbol.for('DataProcessing.SentimentCalculationStage'),
  SentimentStrategyFactory: Symbol.for('DataProcessing.SentimentStrategyFactory'),

  // 风险分析
  RiskDataFetchingStage: Symbol.for('DataProcessing.RiskDataFetchingStage'),
  RiskCalculationStage: Symbol.for('DataProcessing.RiskCalculationStage'),
  RiskDataStrategy: Symbol.for('DataProcessing.RiskDataStrategy'),
} as const;
