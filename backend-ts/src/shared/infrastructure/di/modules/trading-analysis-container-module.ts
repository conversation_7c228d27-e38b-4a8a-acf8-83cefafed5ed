import { ContainerModule, interfaces } from 'inversify';
import { TYPES } from '../types';
import { DegradationManagerService } from '../../../../contexts/trading-signals/application/services/degradation-manager-service';

/**
 * 交易分析向后兼容模块
 *
 * ✅ trading-analysis系统已完全迁移到trend-analysis系统
 *
 * 迁移完成状态：
 * - 技术分析 → trend-analysis系统
 * - 基本面分析 → FundamentalAnalysisModule
 * - 情绪分析 → SentimentAnalysisModule
 * - 量化分析 → QuantitativeAnalysisModule
 * - 四维度融合 → FourDimensionFusionCoordinator
 *
 * 本模块仅保留向后兼容性绑定，确保现有代码正常工作
 */
export const tradingAnalysisContainerModule = new ContainerModule(async (bind: interfaces.Bind) => {
  console.log('🔄 加载trading-analysis向后兼容绑定...');

  try {
    // 技术指标计算器 - 使用统一技术指标计算器替代重复实现
    // 解决CRIT-002问题：消除多重技术指标实现冲突
    const { UnifiedTechnicalIndicatorCalculator } = await import('../../technical-indicators/unified-technical-indicator-calculator');
    bind(TYPES.TradingAnalysis.TechnicalIndicatorCalculator).to(UnifiedTechnicalIndicatorCalculator);

    // 信号融合系统（已迁移到trend-analysis）
    const { SignalFusionAdapter, EnhancedSignalFusionAdapter } = await import('../../../../contexts/trend-analysis/infrastructure/adapters/signal-fusion-adapter');

    // 向后兼容绑定 - 使用trend-analysis系统的适配器
    bind(TYPES.TradingAnalysis.SignalFusionCoordinator).to(SignalFusionAdapter);
    bind(TYPES.TradingAnalysis.EnhancedSignalFusionCoordinator).to(EnhancedSignalFusionAdapter);

    // 真实信号生成服务和生产信号服务 - 使用LazyServiceIdentifer解决循环依赖问题
    try {
      // 导入LazyServiceIdentifer以解决循环依赖
      const { LazyServiceIdentifer } = await import('inversify');
      
      // 同时导入两个相互依赖的服务
      const { RealSignalGenerationService } = await import('../../../../contexts/trading-signals/application/services/real-signal-generation-service');
      const { ProductionSignalService } = await import('../../../../contexts/trading-signals/application/services/production-signal-service');
      
      // 先绑定RealSignalGenerationService
      bind(TYPES.TradingAnalysis.RealSignalGenerationService).to(RealSignalGenerationService).inSingletonScope();
      
      // 再绑定ProductionSignalService
      bind(TYPES.TradingAnalysis.ProductionSignalService).to(ProductionSignalService).inSingletonScope();
      
      // 绑定降级管理服务
      bind(TYPES.TradingSignals.DegradationManagerService).to(DegradationManagerService).inSingletonScope();
      
      console.log('✅ 信号生成服务绑定成功 - RealSignalGenerationService、ProductionSignalService和DegradationManagerService已绑定');
    } catch (error) {
      console.error('❌ 信号生成服务绑定失败:', error);
      console.log('⚠️ 信号生成服务绑定失败，使用降级实现');

      // 为RealSignalGenerationService提供降级实现
      bind(TYPES.TradingAnalysis.RealSignalGenerationService).toDynamicValue(() => {
        return {
          async generateBTCSignal() {
            console.log('🔄 使用RealSignalGenerationService降级实现');
            return {
              id: `fallback-signal-${Date.now()}`,
              symbol: 'BTC',
              signalType: 'HOLD' as const,
              strength: 5,
              confidence: 0.5,
              strategyUsed: 'fallback',
              riskLevel: 'MEDIUM' as const,
              executionContext: {
                marketConditions: 'unknown',
                riskAssessment: 'fallback',
                userProfile: null
              },
              strategyAnalysis: {
                recommendation: 'fallback implementation',
                confidence: 0.5,
                reasoning: ['降级实现'],
                keyFactors: ['服务不可用']
              },
              dataQuality: {
                overall: 0.3,
                sources: ['fallback'],
                freshness: 0.1
              },
              timestamp: new Date(),
              expiresAt: new Date(Date.now() + 15 * 60 * 1000)
            };
          }
        };
      });
      
      // 为ProductionSignalService提供降级实现
      bind(TYPES.TradingAnalysis.ProductionSignalService).toDynamicValue(() => {
        return {
          async generateProductionSignal(request: any) {
            console.log('🔄 使用ProductionSignalService降级实现 - 返回NO_SIGNAL');
            return {
              symbol: request?.symbol || 'BTC',
              signalType: 'NO_SIGNAL',
              strength: 0,
              confidence: 0,
              timestamp: new Date(),
              expiresAt: new Date(Date.now() + 5 * 60 * 1000), // 更短的过期时间
              dataQuality: {
                overall: 0,
                sources: ['degraded'],
                freshness: 0
              },
              message: '信号生成服务处于降级状态，无法提供可靠的交易信号。请手动分析市场或等待服务恢复。',
              warningLevel: 'CRITICAL',
              systemStatus: 'DEGRADED'
            };
          }
        };
      }).inSingletonScope();
    }

    console.log('✅ trading-analysis向后兼容绑定完成');
  } catch (error) {
    console.error('❌ trading-analysis向后兼容绑定失败:', error);
    throw error;
  }
});
