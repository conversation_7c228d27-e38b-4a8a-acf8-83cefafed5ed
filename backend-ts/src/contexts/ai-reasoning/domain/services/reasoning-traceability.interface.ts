/**
 * AI推理可追溯性接口
 * 提供推理过程记录、决策路径追踪、推理步骤可视化等功能
 */

/**
 * 推理过程记录器接口
 * 负责记录AI推理的每个步骤和决策过程
 */
export interface ReasoningProcessRecorder {
  /**
   * 开始记录推理会话
   */
  startReasoningSession(sessionId: string, request: any): Promise<ReasoningSession>;

  /**
   * 记录推理步骤
   */
  recordReasoningStep(sessionId: string, step: ReasoningStepRecord): Promise<void>;

  /**
   * 记录决策点
   */
  recordDecisionPoint(sessionId: string, decision: DecisionPointRecord): Promise<void>;

  /**
   * 记录AI模型调用
   */
  recordModelInvocation(sessionId: string, invocation: ModelInvocationRecord): Promise<void>;

  /**
   * 结束推理会话
   */
  endReasoningSession(sessionId: string, result: any): Promise<void>;

  /**
   * 获取推理会话记录
   */
  getReasoningSession(sessionId: string): Promise<ReasoningSession | null>;
}

/**
 * 决策路径追踪器接口
 * 提供决策路径的完整追踪和回溯功能
 */
export interface DecisionPathTracker {
  /**
   * 创建决策路径
   */
  createDecisionPath(pathId: string, initialContext: any): Promise<DecisionPath>;

  /**
   * 添加决策节点
   */
  addDecisionNode(pathId: string, node: DecisionNode): Promise<void>;

  /**
   * 记录分支决策
   */
  recordBranchDecision(pathId: string, branch: DecisionBranch): Promise<void>;

  /**
   * 获取决策路径
   */
  getDecisionPath(pathId: string): Promise<DecisionPath | null>;

  /**
   * 回溯决策路径
   */
  traceDecisionPath(pathId: string, targetNodeId?: string): Promise<DecisionPathTrace>;

  /**
   * 分析决策影响
   */
  analyzeDecisionImpact(pathId: string, nodeId: string): Promise<DecisionImpactAnalysis>;
}

/**
 * 推理可视化服务接口
 * 提供推理过程的可视化展示和分析功能
 */
export interface ReasoningVisualizationService {
  /**
   * 生成推理流程图
   */
  generateReasoningFlowChart(sessionId: string): Promise<ReasoningFlowChart>;

  /**
   * 生成决策树
   */
  generateDecisionTree(pathId: string): Promise<DecisionTree>;

  /**
   * 生成推理时间线
   */
  generateReasoningTimeline(sessionId: string): Promise<ReasoningTimeline>;

  /**
   * 生成置信度分析图
   */
  generateConfidenceAnalysis(sessionId: string): Promise<ConfidenceAnalysisChart>;

  /**
   * 导出推理报告
   */
  exportReasoningReport(sessionId: string, format: 'pdf' | 'html' | 'json'): Promise<ReasoningReport>;
}

/**
 * 推理审计服务接口
 * 提供推理过程的审计和合规检查功能
 */
export interface ReasoningAuditService {
  /**
   * 审计推理过程
   */
  auditReasoningProcess(sessionId: string): Promise<ReasoningAuditResult>;

  /**
   * 检查推理合规性
   */
  checkReasoningCompliance(sessionId: string, rules: ComplianceRule[]): Promise<ComplianceCheckResult>;

  /**
   * 生成审计报告
   */
  generateAuditReport(sessionId: string): Promise<AuditReport>;

  /**
   * 记录审计事件
   */
  recordAuditEvent(event: AuditEvent): Promise<void>;
}

// ==================== 数据结构定义 ====================

/**
 * 推理会话
 */
export interface ReasoningSession {
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'failed' | 'cancelled';
  request: any;
  result?: any;
  steps: ReasoningStepRecord[];
  decisions: DecisionPointRecord[];
  modelInvocations: ModelInvocationRecord[];
  metadata: Record<string, any>;
}

/**
 * 推理步骤记录
 */
export interface ReasoningStepRecord {
  stepId: string;
  stepNumber: number;
  stepType: 'analysis' | 'synthesis' | 'evaluation' | 'decision';
  description: string;
  input: any;
  output: any;
  reasoning: string;
  duration: number;
  timestamp: Date;
  dependencies: string[];
  metadata: Record<string, any>;
}

/**
 * 决策点记录
 */
export interface DecisionPointRecord {
  decisionId: string;
  decisionType: 'binary' | 'multiple' | 'continuous';
  question: string;
  options: DecisionOption[];
  selectedOption: string;
  reasoning: string;
  confidence: number;
  timestamp: Date;
  context: Record<string, any>;
}

/**
 * 决策选项
 */
export interface DecisionOption {
  optionId: string;
  label: string;
  description: string;
  score: number;
  pros: string[];
  cons: string[];
  riskLevel: 'low' | 'medium' | 'high';
}

/**
 * AI模型调用记录
 */
export interface ModelInvocationRecord {
  invocationId: string;
  modelName: string;
  provider: string;
  prompt: string;
  response: string;
  parameters: Record<string, any>;
  duration: number;
  tokenUsage: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  cost?: number;
  timestamp: Date;
  success: boolean;
  error?: string;
}

/**
 * 决策路径
 */
export interface DecisionPath {
  pathId: string;
  startTime: Date;
  endTime?: Date;
  status: 'active' | 'completed' | 'abandoned';
  initialContext: any;
  nodes: DecisionNode[];
  branches: DecisionBranch[];
  finalDecision?: any;
  metadata: Record<string, any>;
}

/**
 * 决策节点
 */
export interface DecisionNode {
  nodeId: string;
  nodeType: 'input' | 'process' | 'decision' | 'output';
  label: string;
  description: string;
  input: any;
  output: any;
  reasoning: string;
  confidence: number;
  timestamp: Date;
  parentNodes: string[];
  childNodes: string[];
  metadata: Record<string, any>;
}

/**
 * 决策分支
 */
export interface DecisionBranch {
  branchId: string;
  fromNodeId: string;
  toNodeId: string;
  condition: string;
  probability: number;
  reasoning: string;
  weight: number;
  metadata: Record<string, any>;
}

/**
 * 决策路径追踪结果
 */
export interface DecisionPathTrace {
  pathId: string;
  traceSteps: TraceStep[];
  keyDecisions: DecisionNode[];
  criticalBranches: DecisionBranch[];
  alternativePaths: AlternativePath[];
  summary: string;
}

/**
 * 追踪步骤
 */
export interface TraceStep {
  stepNumber: number;
  nodeId: string;
  action: string;
  reasoning: string;
  impact: string;
  timestamp: Date;
}

/**
 * 替代路径
 */
export interface AlternativePath {
  pathId: string;
  description: string;
  probability: number;
  expectedOutcome: any;
  reasoning: string;
}

/**
 * 决策影响分析
 */
export interface DecisionImpactAnalysis {
  nodeId: string;
  directImpact: ImpactMetrics;
  indirectImpact: ImpactMetrics;
  cascadingEffects: CascadingEffect[];
  riskAssessment: RiskAssessment;
  recommendations: string[];
}

/**
 * 影响指标
 */
export interface ImpactMetrics {
  scope: 'local' | 'regional' | 'global';
  confidence: number; // 0-1
  timeframe: string;
  affectedAreas: string[];
}

/**
 * 级联效应
 */
export interface CascadingEffect {
  effectId: string;
  description: string;
  probability: number;
  severity: 'low' | 'medium' | 'high';
  timeDelay: number; // milliseconds
  mitigation: string[];
}

/**
 * 风险评估
 */
export interface RiskAssessment {
  overallRisk: 'low' | 'medium' | 'high';
  riskFactors: RiskFactor[];
  mitigationStrategies: string[];
  contingencyPlans: string[];
}

/**
 * 风险因素
 */
export interface RiskFactor {
  factorId: string;
  description: string;
  probability: number;
  impact: number;
  severity: 'low' | 'medium' | 'high';
  category: string;
}

// ==================== 可视化相关接口 ====================

/**
 * 推理流程图
 */
export interface ReasoningFlowChart {
  chartId: string;
  sessionId: string;
  nodes: FlowChartNode[];
  edges: FlowChartEdge[];
  layout: ChartLayout;
  metadata: Record<string, any>;
}

/**
 * 流程图节点
 */
export interface FlowChartNode {
  nodeId: string;
  type: 'start' | 'process' | 'decision' | 'end';
  label: string;
  description: string;
  position: Position;
  style: NodeStyle;
  data: any;
}

/**
 * 流程图边
 */
export interface FlowChartEdge {
  edgeId: string;
  fromNodeId: string;
  toNodeId: string;
  label?: string;
  style: EdgeStyle;
  weight: number;
}

/**
 * 决策树
 */
export interface DecisionTree {
  treeId: string;
  pathId: string;
  rootNode: TreeNode;
  depth: number;
  totalNodes: number;
  metadata: Record<string, any>;
}

/**
 * 树节点
 */
export interface TreeNode {
  nodeId: string;
  label: string;
  value: any;
  confidence: number;
  children: TreeNode[];
  parent?: string;
  level: number;
  isLeaf: boolean;
}

/**
 * 推理时间线
 */
export interface ReasoningTimeline {
  timelineId: string;
  sessionId: string;
  startTime: Date;
  endTime: Date;
  totalDuration: number;
  events: TimelineEvent[];
  milestones: Milestone[];
}

/**
 * 时间线事件
 */
export interface TimelineEvent {
  eventId: string;
  timestamp: Date;
  type: 'step' | 'decision' | 'model_call' | 'milestone';
  title: string;
  description: string;
  duration?: number;
  importance: 'low' | 'medium' | 'high';
  data: any;
}

/**
 * 里程碑
 */
export interface Milestone {
  milestoneId: string;
  timestamp: Date;
  title: string;
  description: string;
  achievement: string;
  significance: 'minor' | 'major' | 'critical';
}

/**
 * 置信度分析图
 */
export interface ConfidenceAnalysisChart {
  chartId: string;
  sessionId: string;
  overallConfidence: number;
  confidenceDistribution: ConfidenceDistribution[];
  confidenceTrend: ConfidenceTrendPoint[];
  uncertaintyFactors: UncertaintyFactor[];
}

/**
 * 置信度分布
 */
export interface ConfidenceDistribution {
  range: string; // e.g., "0.8-0.9"
  count: number;
  percentage: number;
  steps: string[];
}

/**
 * 置信度趋势点
 */
export interface ConfidenceTrendPoint {
  stepNumber: number;
  confidence: number;
  timestamp: Date;
  reasoning: string;
}

/**
 * 不确定性因素
 */
export interface UncertaintyFactor {
  factorId: string;
  description: string;
  impact: number; // 0-1
  category: string;
  mitigation?: string;
}

/**
 * 推理报告
 */
export interface ReasoningReport {
  reportId: string;
  sessionId: string;
  format: 'pdf' | 'html' | 'json';
  generatedAt: Date;
  content: ReportContent;
  attachments: ReportAttachment[];
}

/**
 * 报告内容
 */
export interface ReportContent {
  summary: ReportSummary;
  detailedAnalysis: DetailedAnalysis;
  visualizations: VisualizationReference[];
  recommendations: string[];
  appendices: ReportAppendix[];
}

/**
 * 报告摘要
 */
export interface ReportSummary {
  sessionOverview: string;
  keyFindings: string[];
  mainConclusions: string[];
  confidenceLevel: number;
  executionTime: number;
}

/**
 * 详细分析
 */
export interface DetailedAnalysis {
  reasoningSteps: StepAnalysis[];
  decisionPoints: DecisionAnalysis[];
  modelPerformance: ModelPerformanceAnalysis;
  riskAssessment: RiskAssessment;
}

/**
 * 步骤分析
 */
export interface StepAnalysis {
  stepId: string;
  analysis: string;
  strengths: string[];
  weaknesses: string[];
  improvements: string[];
}

/**
 * 决策分析
 */
export interface DecisionAnalysis {
  decisionId: string;
  analysis: string;
  alternatives: AlternativeAnalysis[];
  justification: string;
  risks: string[];
}

/**
 * 替代方案分析
 */
export interface AlternativeAnalysis {
  optionId: string;
  analysis: string;
  pros: string[];
  cons: string[];
  feasibility: number;
}

/**
 * 模型性能分析
 */
export interface ModelPerformanceAnalysis {
  totalInvocations: number;
  averageResponseTime: number;
  tokenUsage: TokenUsageStats;
  errorRate: number;
  costAnalysis: CostAnalysis;
}

/**
 * Token使用统计
 */
export interface TokenUsageStats {
  totalTokens: number;
  promptTokens: number;
  completionTokens: number;
  averageTokensPerCall: number;
  efficiency: number;
}

/**
 * 成本分析
 */
export interface CostAnalysis {
  totalCost: number;
  costPerToken: number;
  costPerDecision: number;
  costEfficiency: number;
}

// ==================== 审计相关接口 ====================

/**
 * 推理审计结果
 */
export interface ReasoningAuditResult {
  auditId: string;
  sessionId: string;
  auditedAt: Date;
  auditor: string;
  findings: AuditFinding[];
  recommendations: AuditRecommendation[];
  complianceStatus: 'compliant' | 'non-compliant' | 'partial';
}

/**
 * 审计发现
 */
export interface AuditFinding {
  findingId: string;
  category: 'logic' | 'bias' | 'transparency' | 'accuracy' | 'ethics';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  evidence: string[];
  impact: string;
  recommendation: string;
}

/**
 * 审计建议
 */
export interface AuditRecommendation {
  recommendationId: string;
  priority: 'low' | 'medium' | 'high';
  category: string;
  description: string;
  actionItems: string[];
  expectedBenefit: string;
  implementationEffort: 'low' | 'medium' | 'high';
}

/**
 * 合规检查结果
 */
export interface ComplianceCheckResult {
  checkId: string;
  sessionId: string;
  checkedAt: Date;
  rules: ComplianceRuleResult[];
  violations: ComplianceViolation[];
  status: 'pass' | 'fail' | 'warning';
}

/**
 * 合规规则
 */
export interface ComplianceRule {
  ruleId: string;
  name: string;
  description: string;
  category: string;
  severity: 'low' | 'medium' | 'high';
  checkFunction: (session: ReasoningSession) => boolean;
}

/**
 * 合规规则结果
 */
export interface ComplianceRuleResult {
  ruleId: string;
  ruleName: string;
  status: 'pass' | 'fail' | 'warning';
  score: number;
  details: string;
  evidence: string[];
}

/**
 * 合规违规
 */
export interface ComplianceViolation {
  violationId: string;
  ruleId: string;
  severity: 'low' | 'medium' | 'high';
  description: string;
  location: string; // 违规位置
  remediation: string;
  impact: string;
}

/**
 * 审计报告
 */
export interface AuditReport {
  reportId: string;
  sessionId: string;
  auditType: 'routine' | 'targeted' | 'compliance';
  generatedAt: Date;
  auditor: string;
  executiveSummary: string;
  detailedFindings: AuditFinding[];
  complianceAssessment: ComplianceCheckResult;
  recommendations: AuditRecommendation[];
  actionPlan: ActionPlan;
}

/**
 * 行动计划
 */
export interface ActionPlan {
  planId: string;
  objectives: string[];
  actions: PlannedAction[];
  timeline: string;
  resources: string[];
  successMetrics: string[];
}

/**
 * 计划行动
 */
export interface PlannedAction {
  actionId: string;
  description: string;
  priority: 'low' | 'medium' | 'high';
  assignee: string;
  dueDate: Date;
  dependencies: string[];
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
}

/**
 * 审计事件
 */
export interface AuditEvent {
  eventId: string;
  eventType: 'access' | 'modification' | 'deletion' | 'export' | 'analysis';
  sessionId: string;
  userId: string;
  timestamp: Date;
  action: string;
  details: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
}

// ==================== 辅助类型定义 ====================

/**
 * 位置
 */
export interface Position {
  x: number;
  y: number;
}

/**
 * 节点样式
 */
export interface NodeStyle {
  backgroundColor: string;
  borderColor: string;
  textColor: string;
  shape: 'rectangle' | 'circle' | 'diamond' | 'ellipse';
  size: 'small' | 'medium' | 'large';
}

/**
 * 边样式
 */
export interface EdgeStyle {
  color: string;
  width: number;
  style: 'solid' | 'dashed' | 'dotted';
  arrowType: 'none' | 'arrow' | 'diamond';
}

/**
 * 图表布局
 */
export interface ChartLayout {
  type: 'hierarchical' | 'force' | 'circular' | 'grid';
  direction: 'top-bottom' | 'left-right' | 'bottom-top' | 'right-left';
  spacing: {
    nodeSpacing: number;
    levelSpacing: number;
  };
}

/**
 * 可视化引用
 */
export interface VisualizationReference {
  type: 'flowchart' | 'decision_tree' | 'timeline' | 'confidence_chart';
  id: string;
  title: string;
  description: string;
  url?: string;
}

/**
 * 报告附件
 */
export interface ReportAttachment {
  attachmentId: string;
  name: string;
  type: string;
  size: number;
  url: string;
  description: string;
}

/**
 * 报告附录
 */
export interface ReportAppendix {
  appendixId: string;
  title: string;
  content: string;
  type: 'data' | 'methodology' | 'references' | 'glossary';
}
