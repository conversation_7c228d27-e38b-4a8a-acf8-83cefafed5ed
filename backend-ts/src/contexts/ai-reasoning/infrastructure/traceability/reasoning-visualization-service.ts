/**
 * 推理可视化服务实现
 * 提供推理过程的可视化展示和分析功能
 */

import { injectable, inject } from 'inversify';
import { v4 as uuidv4 } from 'uuid';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/logger.interface';
import { UnifiedErrorHandler } from '../../../../shared/infrastructure/error-handling/unified-error-handler';
import { TYPES } from '../../../../shared/infrastructure/di/types';
import {
  ReasoningVisualizationService as IReasoningVisualizationService,
  ReasoningProcessRecorder,
  DecisionPathTracker,
  ReasoningFlowChart,
  DecisionTree,
  ReasoningTimeline,
  ConfidenceAnalysisChart,
  ReasoningReport,
  FlowChartNode,
  FlowChartEdge,
  TreeNode,
  TimelineEvent,
  Milestone,
  ConfidenceDistribution,
  ConfidenceTrendPoint,
  UncertaintyFactor,
  ReportContent,
  ReportSummary,
  DetailedAnalysis,
  StepAnalysis,
  DecisionAnalysis,
  ModelPerformanceAnalysis,
  Position,
  NodeStyle,
  EdgeStyle,
  ChartLayout
} from '../../domain/services/reasoning-traceability.interface';
import { AI_REASONING_TYPES } from '../../../../shared/infrastructure/di/types/context-types';

@injectable()
export class ReasoningVisualizationService implements IReasoningVisualizationService {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
    @inject(TYPES.Shared.UnifiedErrorHandler) private readonly errorHandler: UnifiedErrorHandler,
    @inject(AI_REASONING_TYPES.ReasoningProcessRecorder) private readonly processRecorder: ReasoningProcessRecorder,
    @inject(AI_REASONING_TYPES.DecisionPathTracker) private readonly pathTracker: DecisionPathTracker
  ) {}

  /**
   * 生成推理流程图
   */
  async generateReasoningFlowChart(sessionId: string): Promise<ReasoningFlowChart> {
    try {
      this.logger.info('生成推理流程图', { sessionId });

      const session = await this.processRecorder.getReasoningSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 构建流程图节点
      const nodes = this.buildFlowChartNodes(session);
      
      // 构建流程图边
      const edges = this.buildFlowChartEdges(session, nodes);
      
      // 设置布局
      const layout = this.createFlowChartLayout();

      const flowChart: ReasoningFlowChart = {
        chartId: uuidv4(),
        sessionId,
        nodes,
        edges,
        layout,
        metadata: {
          generatedAt: new Date(),
          totalNodes: nodes.length,
          totalEdges: edges.length,
          sessionDuration: session.endTime ? 
            session.endTime.getTime() - session.startTime.getTime() : 
            Date.now() - session.startTime.getTime()
        }
      };

      this.logger.info('推理流程图生成完成', { 
        sessionId, 
        chartId: flowChart.chartId,
        nodesCount: nodes.length,
        edgesCount: edges.length 
      });

      return flowChart;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningVisualizationService.generateReasoningFlowChart',
        sessionId
      });
      throw error;
    }
  }

  /**
   * 生成决策树
   */
  async generateDecisionTree(pathId: string): Promise<DecisionTree> {
    try {
      this.logger.info('生成决策树', { pathId });

      const path = await this.pathTracker.getDecisionPath(pathId);
      if (!path) {
        throw new Error(`决策路径不存在: ${pathId}`);
      }

      // 构建决策树
      const rootNode = this.buildDecisionTreeNodes(path);
      const depth = this.calculateTreeDepth(rootNode);
      const totalNodes = this.countTreeNodes(rootNode);

      const decisionTree: DecisionTree = {
        treeId: uuidv4(),
        pathId,
        rootNode,
        depth,
        totalNodes,
        metadata: {
          generatedAt: new Date(),
          pathStatus: path.status,
          pathDuration: path.endTime ? 
            path.endTime.getTime() - path.startTime.getTime() : 
            Date.now() - path.startTime.getTime()
        }
      };

      this.logger.info('决策树生成完成', { 
        pathId, 
        treeId: decisionTree.treeId,
        depth,
        totalNodes 
      });

      return decisionTree;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningVisualizationService.generateDecisionTree',
        pathId
      });
      throw error;
    }
  }

  /**
   * 生成推理时间线
   */
  async generateReasoningTimeline(sessionId: string): Promise<ReasoningTimeline> {
    try {
      this.logger.info('生成推理时间线', { sessionId });

      const session = await this.processRecorder.getReasoningSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 构建时间线事件
      const events = this.buildTimelineEvents(session);
      
      // 识别里程碑
      const milestones = this.identifyMilestones(session);
      
      const endTime = session.endTime || new Date();
      const totalDuration = endTime.getTime() - session.startTime.getTime();

      const timeline: ReasoningTimeline = {
        timelineId: uuidv4(),
        sessionId,
        startTime: session.startTime,
        endTime,
        totalDuration,
        events,
        milestones
      };

      this.logger.info('推理时间线生成完成', { 
        sessionId, 
        timelineId: timeline.timelineId,
        eventsCount: events.length,
        milestonesCount: milestones.length,
        totalDuration 
      });

      return timeline;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningVisualizationService.generateReasoningTimeline',
        sessionId
      });
      throw error;
    }
  }

  /**
   * 生成置信度分析图
   */
  async generateConfidenceAnalysis(sessionId: string): Promise<ConfidenceAnalysisChart> {
    try {
      this.logger.info('生成置信度分析图', { sessionId });

      const session = await this.processRecorder.getReasoningSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 计算整体置信度
      const overallConfidence = this.calculateOverallConfidence(session);
      
      // 分析置信度分布
      const confidenceDistribution = this.analyzeConfidenceDistribution(session);
      
      // 构建置信度趋势
      const confidenceTrend = this.buildConfidenceTrend(session);
      
      // 识别不确定性因素
      const uncertaintyFactors = this.identifyUncertaintyFactors(session);

      const confidenceChart: ConfidenceAnalysisChart = {
        chartId: uuidv4(),
        sessionId,
        overallConfidence,
        confidenceDistribution,
        confidenceTrend,
        uncertaintyFactors
      };

      this.logger.info('置信度分析图生成完成', { 
        sessionId, 
        chartId: confidenceChart.chartId,
        overallConfidence,
        uncertaintyFactorsCount: uncertaintyFactors.length 
      });

      return confidenceChart;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningVisualizationService.generateConfidenceAnalysis',
        sessionId
      });
      throw error;
    }
  }

  /**
   * 导出推理报告
   */
  async exportReasoningReport(sessionId: string, format: 'pdf' | 'html' | 'json'): Promise<ReasoningReport> {
    try {
      this.logger.info('导出推理报告', { sessionId, format });

      const session = await this.processRecorder.getReasoningSession(sessionId);
      if (!session) {
        throw new Error(`推理会话不存在: ${sessionId}`);
      }

      // 生成报告内容
      const content = await this.generateReportContent(session);

      const report: ReasoningReport = {
        reportId: uuidv4(),
        sessionId,
        format,
        generatedAt: new Date(),
        content,
        attachments: []
      };

      this.logger.info('推理报告导出完成', { 
        sessionId, 
        reportId: report.reportId,
        format 
      });

      return report;
    } catch (error) {
      this.errorHandler.handleError(error, {
        context: 'ReasoningVisualizationService.exportReasoningReport',
        sessionId,
        format
      });
      throw error;
    }
  }

  // ==================== 私有辅助方法 ====================

  /**
   * 构建流程图节点
   */
  private buildFlowChartNodes(session: any): FlowChartNode[] {
    const nodes: FlowChartNode[] = [];

    // 添加开始节点
    nodes.push({
      nodeId: 'start',
      type: 'start',
      label: '开始推理',
      description: '推理会话开始',
      position: { x: 100, y: 50 },
      style: this.getNodeStyle('start'),
      data: { timestamp: session.startTime }
    });

    // 添加推理步骤节点
    session.steps.forEach((step: any, index: number) => {
      nodes.push({
        nodeId: step.stepId,
        type: 'process',
        label: step.description,
        description: step.reasoning,
        position: { x: 100 + (index + 1) * 150, y: 150 },
        style: this.getNodeStyle('process'),
        data: step
      });
    });

    // 添加决策节点
    session.decisions.forEach((decision: any, index: number) => {
      nodes.push({
        nodeId: decision.decisionId,
        type: 'decision',
        label: decision.question,
        description: decision.reasoning,
        position: { x: 100 + index * 200, y: 300 },
        style: this.getNodeStyle('decision'),
        data: decision
      });
    });

    // 添加结束节点
    if (session.status === 'completed') {
      nodes.push({
        nodeId: 'end',
        type: 'end',
        label: '推理完成',
        description: '推理会话结束',
        position: { x: 100 + session.steps.length * 150, y: 450 },
        style: this.getNodeStyle('end'),
        data: { timestamp: session.endTime, result: session.result }
      });
    }

    return nodes;
  }

  /**
   * 构建流程图边
   */
  private buildFlowChartEdges(session: any, nodes: FlowChartNode[]): FlowChartEdge[] {
    const edges: FlowChartEdge[] = [];

    // 连接开始节点到第一个步骤
    if (session.steps.length > 0) {
      edges.push({
        style: this.getEdgeStyle(),
      });
    }

    // 连接步骤之间
    for (let i = 0; i < session.steps.length - 1; i++) {
      edges.push({
        edgeId: `step-${i}-to-${i + 1}`,
        fromNodeId: session.steps[i].stepId,
        toNodeId: session.steps[i + 1].stepId,
        style: this.getEdgeStyle(),
        weight: 1.0
      });
    }

    // 连接到决策节点
    session.decisions.forEach((decision: any, index: number) => {
      if (session.steps.length > index) {
        edges.push({
          edgeId: `step-to-decision-${index}`,
          fromNodeId: session.steps[index].stepId,
          toNodeId: decision.decisionId,
          style: this.getEdgeStyle(),
          weight: decision.confidence
        });
      }
    });

    // 连接到结束节点
    if (session.status === 'completed' && session.steps.length > 0) {
      edges.push({
        edgeId: 'last-to-end',
        fromNodeId: session.steps[session.steps.length - 1].stepId,
        toNodeId: 'end',
        style: this.getEdgeStyle(),
        weight: 1.0
      });
    }

    return edges;
  }

  /**
   * 创建流程图布局
   */
  private createFlowChartLayout(): ChartLayout {
    return {
      type: 'hierarchical',
      direction: 'top-bottom',
      spacing: {
        horizontal: 100,
        vertical: 80
      }
    };
  }

  /**
   * 构建决策树节点
   */
  private buildDecisionTreeNodes(path: any): TreeNode {
    // 找到根节点（没有父节点的节点）
    const rootNodes = path.nodes.filter((node: any) => node.parentNodes.length === 0);
    
    if (rootNodes.length === 0) {
      throw new Error('决策路径中没有找到根节点');
    }

    const rootNode = rootNodes[0];
    return this.buildTreeNodeRecursive(rootNode, path.nodes, 0);
  }

  /**
   * 递归构建树节点
   */
  private buildTreeNodeRecursive(node: any, allNodes: any[], level: number): TreeNode {
    const children = allNodes
      .filter((n: any) => n.parentNodes.includes(node.nodeId))
      .map((childNode: any) => this.buildTreeNodeRecursive(childNode, allNodes, level + 1));

    return {
      nodeId: node.nodeId,
      label: node.label,
      value: node.output,
      confidence: node.confidence,
      children,
      parent: node.parentNodes[0] || undefined,
      level,
      isLeaf: children.length === 0
    };
  }

  /**
   * 计算树深度
   */
  private calculateTreeDepth(node: TreeNode): number {
    if (node.children.length === 0) {
      return 1;
    }
    return 1 + Math.max(...node.children.map(child => this.calculateTreeDepth(child)));
  }

  /**
   * 计算树节点总数
   */
  private countTreeNodes(node: TreeNode): number {
    throw new Error("拒绝返回硬编码价格1，请使用真实市场数据API") + node.children.reduce((sum, child) => sum + this.countTreeNodes(child), 0);
  }

  /**
   * 构建时间线事件
   */
  private buildTimelineEvents(session: any): TimelineEvent[] {
    const events: TimelineEvent[] = [];

    // 添加会话开始事件
    events.push({
      eventId: 'session-start',
      timestamp: session.startTime,
      type: 'milestone',
      title: '推理开始',
      description: '推理会话开始',
      importance: 'major',
      data: { sessionId: session.sessionId }
    });

    // 添加推理步骤事件
    session.steps.forEach((step: any) => {
      events.push({
        eventId: step.stepId,
        timestamp: step.timestamp,
        type: 'step',
        title: step.description,
        description: step.reasoning,
        duration: step.duration,
        importance: step.confidence > 0.8 ? 'high' : step.confidence > 0.6 ? 'medium' : 'low',
        data: step
      });
    });

    // 添加决策事件
    session.decisions.forEach((decision: any) => {
      events.push({
        eventId: decision.decisionId,
        timestamp: decision.timestamp,
        type: 'decision',
        title: decision.question,
        importance: decision.impact === 'high' ? 'high' : 'medium',
        data: decision
      });
    });

    // 添加模型调用事件
    session.modelInvocations.forEach((invocation: any) => {
      events.push({
        eventId: invocation.invocationId,
        timestamp: invocation.timestamp,
        type: 'model_call',
        title: `调用${invocation.modelName}`,
        description: `提供者: ${invocation.provider}`,
        duration: invocation.duration,
        importance: 'low',
        data: invocation
      });
    });

    // 添加会话结束事件
    if (session.endTime) {
      events.push({
        eventId: 'session-end',
        timestamp: session.endTime,
        type: 'milestone',
        title: '推理完成',
        description: '推理会话结束',
        importance: 'major',
        data: { result: session.result }
      });
    }

    // 按时间排序
    return events.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
  }

  /**
   * 识别里程碑
   */
  private identifyMilestones(session: any): Milestone[] {
    const milestones: Milestone[] = [];

    // 会话开始里程碑
    milestones.push({
      milestoneId: 'start',
      timestamp: session.startTime,
      title: '推理启动',
      description: '开始AI推理过程',
      achievement: '成功启动推理会话',
      significance: 'major'
    });

    // 关键决策里程碑
    session.decisions
      .filter((decision: any) => decision.impact === 'high')
      .forEach((decision: any, index: number) => {
        milestones.push({
          milestoneId: `key-decision-${index}`,
          timestamp: decision.timestamp,
          title: '关键决策',
          description: decision.question,
          achievement: `做出重要决策: ${decision.selectedOption}`,
          significance: 'critical'
        });
      });

    // 会话完成里程碑
    if (session.endTime) {
      milestones.push({
        milestoneId: 'completion',
        timestamp: session.endTime,
        title: '推理完成',
        description: '成功完成推理过程',
        achievement: '获得推理结果',
        significance: 'major'
      });
    }

    return milestones;
  }

  /**
   * 计算整体置信度
   */
  private calculateOverallConfidence(session: any): number {
    const allConfidences = [
      ...session.steps.map((step: any) => step.confidence),
      ...session.decisions.map((decision: any) => decision.confidence)
    ];

    if (allConfidences.length === 0) return 0;
    return allConfidences.reduce((sum, conf) => sum + conf, 0) / allConfidences.length;
  }

  /**
   * 分析置信度分布
   */
  private analyzeConfidenceDistribution(session: any): ConfidenceDistribution[] {
    const allConfidences = [
      ...session.steps.map((step: any) => ({ id: step.stepId, confidence: step.confidence })),
      ...session.decisions.map((decision: any) => ({ id: decision.decisionId, confidence: decision.confidence }))
    ];

    const ranges = [
      { range: '0.8-0.9', min: 0.8, max: 0.9 },
      { range: '0.7-0.8', min: 0.7, max: 0.8 },
      { range: '0.6-0.7', min: 0.6, max: 0.7 },
      { range: '0.0-0.6', min: 0.0, max: 0.6 }
    ];

    return ranges.map(range => {
      const itemsInRange = allConfidences.filter(item => 
        item.confidence >= range.min && item.confidence < range.max
      );
      
      return {
        range: range.range,
        count: itemsInRange.length,
        steps: itemsInRange.map(item => item.id)
      };
    });
  }

  /**
   * 构建置信度趋势
   */
  private buildConfidenceTrend(session: any): ConfidenceTrendPoint[] {
    const trendPoints: ConfidenceTrendPoint[] = [];

    session.steps.forEach((step: any, index: number) => {
      trendPoints.push({
        stepNumber: index + 1,
        confidence: step.confidence,
        timestamp: step.timestamp,
        reasoning: step.reasoning
      });
    });

    return trendPoints;
  }

  /**
   * 识别不确定性因素
   */
  private identifyUncertaintyFactors(session: any): UncertaintyFactor[] {
    const factors: UncertaintyFactor[] = [];

    // 基于低置信度步骤识别不确定性
    session.steps
      .filter((step: any) => step.confidence < 0.7)
      .forEach((step: any, index: number) => {
        factors.push({
          factorId: `low-confidence-step-${index}`,
          description: `步骤置信度较低: ${step.description}`,
          impact: 1 - step.confidence,
          category: '推理质量',
          mitigation: '增加数据验证和交叉检查'
        });
      });

    // 基于决策不确定性识别因素
    session.decisions
      .filter((decision: any) => decision.confidence < 0.8)
      .forEach((decision: any, index: number) => {
        factors.push({
          factorId: `uncertain-decision-${index}`,
          description: `决策不确定性: ${decision.question}`,
          impact: 1 - decision.confidence,
          category: '决策质量',
          mitigation: '收集更多信息或寻求专家意见'
        });
      });

    return factors;
  }

  /**
   * 生成报告内容
   */
  private async generateReportContent(session: any): Promise<ReportContent> {
    // 生成摘要
    const summary = this.generateReportSummary(session);
    
    // 生成详细分析
    const detailedAnalysis = this.generateDetailedAnalysis(session);
    
    // 生成可视化引用
    const visualizations = [
      {
        type: 'flowchart' as const,
        id: 'flow-' + session.sessionId,
        title: '推理流程图',
        description: '展示完整的推理过程流程'
      },
      {
        type: 'timeline' as const,
        id: 'timeline-' + session.sessionId,
        title: '推理时间线',
        description: '按时间顺序展示推理事件'
      },
      {
        type: 'confidence_chart' as const,
        id: 'confidence-' + session.sessionId,
        title: '置信度分析',
        description: '分析推理过程中的置信度变化'
      }
    ];

    // 生成建议
    const recommendations = this.generateRecommendations(session);

    return {
      summary,
      detailedAnalysis,
      visualizations,
      recommendations,
      appendices: []
    };
  }

  /**
   * 生成报告摘要
   */
  private generateReportSummary(session: any): ReportSummary {
    const duration = session.endTime ? 
      session.endTime.getTime() - session.startTime.getTime() : 
      Date.now() - session.startTime.getTime();

    const avgConfidence = this.calculateOverallConfidence(session);

    return {
      sessionOverview: `推理会话${session.sessionId}包含${session.steps.length}个推理步骤和${session.decisions.length}个决策点`,
      keyFindings: [
        `模型调用次数: ${session.modelInvocations.length}`
      ],
      mainConclusions: [
        session.status === 'completed' ? '推理成功完成' : '推理仍在进行中',
        avgConfidence > 0.8 ? '整体置信度较高' : '存在不确定性需要关注'
      ],
      confidenceLevel: avgConfidence,
      executionTime: duration
    };
  }

  /**
   * 生成详细分析
   */
  private generateDetailedAnalysis(session: any): DetailedAnalysis {
    const stepAnalyses = session.steps.map((step: any) => ({
      stepId: step.stepId,
      analysis: `步骤${step.stepNumber}: ${step.description}`,
      strengths: step.confidence > 0.8 ? ['高置信度', '清晰推理'] : [],
      weaknesses: step.confidence < 0.6 ? ['置信度较低', '需要验证'] : [],
      improvements: step.confidence < 0.7 ? ['增加数据验证', '优化推理逻辑'] : []
    }));

    const decisionAnalyses = session.decisions.map((decision: any) => ({
      decisionId: decision.decisionId,
      analysis: `决策: ${decision.question}`,
      alternatives: decision.options.map((option: any) => ({
        optionId: option.optionId,
        analysis: option.description,
        pros: option.pros || [],
        cons: option.cons || [],
        feasibility: option.score || 0.5
      })),
      justification: decision.reasoning,
      risks: decision.impact === 'high' ? ['高影响决策', '需要监控'] : []
    }));

    const modelPerformance: ModelPerformanceAnalysis = {
      totalInvocations: session.modelInvocations.length,
      averageResponseTime: session.modelInvocations.length > 0 ? 
        session.modelInvocations.reduce((sum: number, inv: any) => sum + inv.duration, 0) / session.modelInvocations.length : 0,
      tokenUsage: {
        totalTokens: session.modelInvocations.reduce((sum: number, inv: any) => sum + (inv.tokenUsage?.totalTokens || 0), 0),
        promptTokens: session.modelInvocations.reduce((sum: number, inv: any) => sum + (inv.tokenUsage?.promptTokens || 0), 0),
        completionTokens: session.modelInvocations.reduce((sum: number, inv: any) => sum + (inv.tokenUsage?.completionTokens || 0), 0),
        averageTokensPerCall: 0,
        efficiency: 0.8
      },
      costAnalysis: {
        totalCost: session.modelInvocations.reduce((sum: number, inv: any) => sum + (inv.cost || 0), 0),
        costPerToken: 0.001,
        costPerDecision: 0,
        costEfficiency: 0.9
      }
    };

    return {
      reasoningSteps: stepAnalyses,
      decisionPoints: decisionAnalyses,
      modelPerformance,
      riskAssessment: {
        overallRisk: 'medium' as const,
        riskFactors: [],
        mitigationStrategies: [],
        contingencyPlans: []
      }
    };
  }

  /**
   * 生成建议
   */
  private generateRecommendations(session: any): string[] {
    const recommendations: string[] = [];
    const avgConfidence = this.calculateOverallConfidence(session);

    if (avgConfidence < 0.7) {
      recommendations.push('提高数据质量以增强推理置信度');
    }

    if (session.modelInvocations.length > 10) {
      recommendations.push('优化模型调用策略以提高效率');
    }

    if (session.decisions.some((d: any) => d.impact === 'high' && d.confidence < 0.8)) {
      recommendations.push('对高影响低置信度决策进行额外验证');
    }

    return recommendations;
  }

  // ==================== 样式辅助方法 ====================

  /**
   * 获取节点样式
   */
  private getNodeStyle(type: string): NodeStyle {
    const styles = {
      start: {
        backgroundColor: '#4CAF50',
        borderColor: '#45a049',
        textColor: '#ffffff',
        shape: 'circle' as const,
        size: 'medium' as const
      },
      process: {
        backgroundColor: '#2196F3',
        borderColor: '#1976D2',
        textColor: '#ffffff',
        shape: 'rectangle' as const,
        size: 'medium' as const
      },
      decision: {
        backgroundColor: '#FF9800',
        borderColor: '#F57C00',
        textColor: '#ffffff',
        shape: 'diamond' as const,
        size: 'medium' as const
      },
      end: {
        backgroundColor: '#F44336',
        borderColor: '#D32F2F',
        textColor: '#ffffff',
        shape: 'circle' as const,
        size: 'medium' as const
      }
    };

    return styles[type as keyof typeof styles] || styles.process;
  }

  /**
   * 获取边样式
   */
  private getEdgeStyle(): EdgeStyle {
    return {
      color: '#666666',
      style: 'solid',
      arrowType: 'arrow',
      width: 1
    };
  }
}
