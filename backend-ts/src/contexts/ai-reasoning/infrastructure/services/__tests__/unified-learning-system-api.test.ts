/**
 * 统一学习系统API测试
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { UnifiedLearningSystemStarter } from '../unified-learning-system-starter';
import { UnifiedPredictionEngine } from '../unified-prediction-engine';
import { UnifiedLearningEngine } from '../unified-learning-engine';
import { MacroPredictionService } from '../macro-prediction-service';
import { PrismaClient } from '@prisma/client';
import { Logger } from 'winston';
import { PredictionType, MicroPredictionResult } from '../../../domain/services/unified-learning-engine.interface';

// Mock dependencies
const mockPrisma = {
  symbol: {
    findUnique: vi.fn(),
  },
  symbols: {
    findFirst: vi.fn().mockResolvedValue({ id: 'btc-usdt-id', symbol: 'BTC/USDT' }),
  },
  priceData: {
    findFirst: vi.fn(),
  },
  historicalData: {
    findMany: vi.fn(),
  },
  shortCyclePrediction: {
    findMany: vi.fn(),
    create: vi.fn(),
  },
  learningKnowledgeBase: {
    findFirst: vi.fn(),
    create: vi.fn(),
  },
  macroCyclePrediction: {
    create: vi.fn(),
    findMany: vi.fn(),
  },
} as unknown as PrismaClient;

const mockLogger = {
  info: vi.fn(),
  error: vi.fn(),
  warn: vi.fn(),
  debug: vi.fn(),
} as unknown as Logger;

const mockTechnicalCalculator = {
  calculateSMA: vi.fn().mockReturnValue(50000),
  calculateEMA: vi.fn().mockReturnValue(50000),
  calculateRSI: vi.fn().mockReturnValue(50),
  calculateMACD: vi.fn().mockReturnValue({ macd: 0, signal: 0, histogram: 0 }),
  calculateBollingerBands: vi.fn().mockReturnValue({ upper: 52000, middle: 50000, lower: 48000 }),
} as any;

describe('统一学习系统API测试', () => {
  let predictionEngine: UnifiedPredictionEngine;
  let learningEngine: UnifiedLearningEngine;
  let macroPredictionService: MacroPredictionService;
  let systemStarter: UnifiedLearningSystemStarter;

  beforeEach(() => {
    vi.clearAllMocks();

    // 重新设置mock方法
    mockPrisma.learningKnowledgeBase.findFirst = vi.fn();
    mockPrisma.learningKnowledgeBase.create = vi.fn();

    learningEngine = new UnifiedLearningEngine(mockPrisma, mockLogger);
    predictionEngine = new UnifiedPredictionEngine(
      mockPrisma,
      mockLogger,
      mockTechnicalCalculator,
      {} as any, // parameterCenter
      {} as any, // knowledgeBase
      {} as any  // timeframeCoordinator
    );
    macroPredictionService = new MacroPredictionService(
      mockPrisma,
      mockLogger,
      predictionEngine,
      learningEngine
    );
    systemStarter = new UnifiedLearningSystemStarter(
      mockLogger,
      predictionEngine,
      learningEngine,
      {} as any, // timeframeCoordinator
      macroPredictionService
    );

    // 模拟getHistoricalDataFromBinance方法
    vi.spyOn(predictionEngine as any, 'getHistoricalDataFromBinance').mockResolvedValue([
      { timestamp: new Date(), open: 49000, high: 51000, low: 48000, close: 50000, volume: 1000 },
      { timestamp: new Date(), open: 50000, high: 52000, low: 49000, close: 51000, volume: 1100 },
      { timestamp: new Date(), open: 51000, high: 53000, low: 50000, close: 52000, volume: 1200 },
      { timestamp: new Date(), open: 52000, high: 54000, low: 51000, close: 53000, volume: 1300 },
      { timestamp: new Date(), open: 53000, high: 55000, low: 52000, close: 54000, volume: 1400 }
    ]);
  });

  describe('预测引擎API', () => {
    it('generateMicroPrediction API应该返回正确格式', async () => {
      // Arrange
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      // Act
      const result = await predictionEngine.generateMicroPrediction(marketContext);

      // Assert - 验证API响应格式
      expect(result).toMatchObject({
        predictedValue: expect.any(Number),
        predictedDirection: expect.stringMatching(/^(UP|DOWN)$/),
        confidence: expect.any(Number),
        predictionHorizon: '15m',
        predictionType: 'price_15m',
        predictionTimestamp: expect.any(Date)
      });

      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
      expect(result.predictedValue).toBeGreaterThan(0);
    });

    it('generateMesoPrediction API应该返回正确格式', async () => {
      // Arrange
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 48000,
        volatility: 0.03,
        marketCondition: 'uptrend',
        timestamp: new Date(),
        dataPoints: 200
      };

      // Act
      const result = await predictionEngine.generateMesoPrediction(marketContext);

      // Assert - 验证API响应格式
      expect(result).toMatchObject({
        predictedValue: expect.any(Number),
        predictedDirection: expect.stringMatching(/^(UP|DOWN)$/),
        confidence: expect.any(Number),
        predictionHorizon: '8h',
        predictionType: 'trend_8h',
        predictionTimestamp: expect.any(Date)
      });

      expect(result.confidence).toBeGreaterThanOrEqual(0.4);
      expect(result.confidence).toBeLessThanOrEqual(0.85);
    });

    it('generateMacroPrediction API应该返回正确格式', async () => {
      // Arrange
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 45000,
        volatility: 0.05,
        marketCondition: 'macroStrongUptrend',
        timestamp: new Date(),
        dataPoints: 500
      };

      // Act
      const result = await predictionEngine.generateMacroPrediction(marketContext);

      // Assert - 验证API响应格式
      expect(result).toMatchObject({
        predictedValue: expect.any(Number),
        predictedDirection: expect.stringMatching(/^(UP|DOWN)$/),
        confidence: expect.any(Number),
        predictionHorizon: '3d',
        predictionType: 'strategy_3d',
        predictionTimestamp: expect.any(Date)
      });

      expect(result.confidence).toBeGreaterThanOrEqual(0.5);
      expect(result.confidence).toBeLessThanOrEqual(0.8);
    });

    it('generateComprehensivePrediction API应该返回完整预测', async () => {
      // Arrange
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      // Act
      const result = await predictionEngine.generateComprehensivePrediction(marketContext);

      // Assert - 验证综合预测API响应格式
      expect(result).toMatchObject({
        micro: expect.objectContaining({
          predictionHorizon: '15m',
          predictionType: 'price_15m'
        }),
        meso: expect.objectContaining({
          predictionHorizon: '8h',
          predictionType: 'trend_8h'
        }),
        macro: expect.objectContaining({
          predictionHorizon: '3d',
          predictionType: 'strategy_3d'
        }),
        综合建议: expect.objectContaining({
          strategy: expect.any(String),
          confidenceScore: expect.any(Number)
        })
      });
    });

    it('validatePrediction API应该返回验证结果', async () => {
      // Arrange
      const prediction = {
        symbolId: 'btc-usdt',
        predictionType: PredictionType.PRICE_MICRO,
        predictedValue: 51000,
        predictedDirection: 'UP' as const,
        confidence: 0.75,
        marketContext: {
          currentPrice: 50000,
          avgPrice: 49000,
          volatility: 0.02,
          marketCondition: 'trending',
          timestamp: new Date(),
          dataPoints: 100
        },
        predictionTimestamp: new Date(),
        targetVerificationTime: new Date(),
        predictionHorizon: '15m' as const,
        verificationDelay: '30m' as const,
        modelVersion: 'v1.0'
      } as MicroPredictionResult;

      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      // Act
      const result = await predictionEngine.validatePrediction(prediction, marketContext);

      // Assert - 验证API响应格式
      expect(result).toMatchObject({
        isValid: expect.any(Boolean),
        validationScore: expect.any(Number),
        warnings: expect.any(Array),
        recommendations: expect.any(Array)
      });

      expect(result.validationScore).toBeGreaterThanOrEqual(0);
      expect(result.validationScore).toBeLessThanOrEqual(1);
    });
  });

  describe('学习引擎API', () => {
    it('learnFromVerification API应该返回学习结果', async () => {
      // Arrange
      const prediction = {
        symbolId: 'btc-usdt',
        predictionType: PredictionType.PRICE_MICRO,
        predictedValue: 51000,
        predictedDirection: 'UP' as const,
        confidence: 0.75,
        marketContext: {
          currentPrice: 50000,
          avgPrice: 49000,
          volatility: 0.02,
          marketCondition: 'trending',
          timestamp: new Date(),
          dataPoints: 100
        },
        predictionTimestamp: new Date(),
        targetVerificationTime: new Date(),
        predictionHorizon: '15m' as const,
        verificationDelay: '30m' as const,
        modelVersion: 'v1.0'
      } as MicroPredictionResult;

      const actualOutcome = {
        actualValue: 50800,
        actualDirection: 'UP' as const,
        verificationTimestamp: new Date(),
        accuracyScore: 0.85
      };

      (mockPrisma.learningKnowledgeBase.findFirst as any).mockResolvedValue(null);
      (mockPrisma.learningKnowledgeBase.create as any).mockResolvedValue({
        id: 'pattern-1',
        timeframe: 'micro',
        marketCondition: 'trending',
        samples: [],
        averageAccuracy: 0,
        lastUpdated: new Date()
      });

      // Act
      const result = await learningEngine.learnFromVerification(
        prediction,
        actualOutcome,
        'micro' as any
      );

      // Assert - 验证学习API响应格式
      expect(result).toMatchObject({
        adjustments: expect.any(Object),
        confidence: expect.any(Number),
        reasoning: expect.any(Array),
        timestamp: expect.any(Date),
        learnedPatterns: expect.any(Array),
        optimizedParameters: expect.any(Object)
      });

      expect(result.confidence).toBeGreaterThan(0);
      expect(result.confidence).toBeLessThanOrEqual(1);
    });

    it('getLearningInsights API应该返回洞察信息', async () => {
      // Arrange
      const timeframe = 'micro' as any;
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      (mockPrisma.learningKnowledgeBase.findFirst as any).mockResolvedValue({
        id: 'pattern-1',
        timeframe: 'micro',
        marketCondition: 'trending',
        samples: [
          { accuracy: 0.8, timestamp: new Date() },
          { accuracy: 0.7, timestamp: new Date() }
        ],
        verifiedSamples: 2,
        averageAccuracy: 0.75,
        lastUpdated: new Date(),
        insights: ['趋势预测准确率较高']
      });

      // Act
      const result = await learningEngine.getLearningInsights(timeframe, marketContext);

      // Assert - 验证洞察API响应格式
      expect(result).toMatchObject({
        timeframe: 'micro',
        totalPredictions: expect.any(Number),
        verifiedPredictions: expect.any(Number),
        accuracyRate: expect.any(Number),
        lastLearning: expect.any(Date),
        patterns: expect.any(Array),
        recommendations: expect.any(Array)
      });

      expect(result.accuracyRate).toBeGreaterThanOrEqual(0);
      expect(result.accuracyRate).toBeLessThanOrEqual(1);
    });

    it('optimizeParameters API应该返回优化结果', async () => {
      // Arrange
      const timeframe = 'micro' as any;
      const performanceMetrics = {
        accuracyRate: 0.75,
        predictionCount: 100,
        correctPredictions: 75,
        averageConfidence: 0.7,
        timeWindow: '7d'
      };

      // Act
      const result = await learningEngine.optimizeParameters(timeframe, performanceMetrics);

      // Assert - 验证优化API响应格式
      expect(result).toMatchObject({
        optimizedParameters: expect.any(Object),
        expectedImprovement: expect.any(Number),
        confidence: expect.any(Number),
        reasoning: expect.any(Array)
      });

      expect(result.expectedImprovement).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeGreaterThan(0);
    });
  });

  describe('宏观预测服务API', () => {
    it('getStatus API应该返回服务状态', () => {
      // Act
      const result = macroPredictionService.getStatus();

      // Assert - 验证状态API响应格式
      expect(result).toMatchObject({
        isRunning: expect.any(Boolean),
        config: expect.any(Object),
        lastUpdate: expect.any(String)
      });

      expect(result.config).toHaveProperty('predictionInterval');
      expect(result.config).toHaveProperty('verificationInterval');
      expect(result.config).toHaveProperty('predictionHorizon');
      expect(result.config).toHaveProperty('verificationDelay');
    });
  });

  describe('系统启动器API', () => {
    it('getSystemStatus API应该返回系统状态', () => {
      // Act
      const result = systemStarter.getSystemStatus();

      // Assert - 验证系统状态API响应格式
      expect(result).toMatchObject({
        isStarted: expect.any(Boolean),
        components: expect.objectContaining({
          macroPredictionService: expect.any(Object),
          predictionEngine: 'active',
          learningEngine: 'active',
          timeframeCoordinator: 'active'
        }),
        version: '3.0',
        startTime: expect.any(String)
      });
    });

    it('healthCheck API应该返回健康状态', async () => {
      // Act
      const result = await systemStarter.healthCheck();

      // Assert - 验证健康检查API响应格式
      expect(result).toMatchObject({
        status: expect.stringMatching(/^(healthy|unhealthy)$/),
        details: expect.any(Object)
      });
    });

    it('getSystemMetrics API应该返回系统指标', async () => {
      // Act
      const result = await systemStarter.getSystemMetrics();

      // Assert - 验证指标API响应格式
      expect(result).toMatchObject({
        system: expect.any(Object),
        timestamp: expect.any(String),
        uptime: expect.any(String)
      });
    });

    it('runDiagnostics API应该返回诊断信息', async () => {
      // Act
      const result = await systemStarter.runDiagnostics();

      // Assert - 验证诊断API响应格式
      expect(result).toMatchObject({
        timestamp: expect.any(String),
        systemStatus: expect.any(Object),
        healthCheck: expect.any(Object),
        components: expect.objectContaining({
          predictionEngine: 'OK',
          learningEngine: 'OK',
          timeframeCoordinator: 'OK',
          macroPredictionService: expect.any(Object)
        })
      });
    });
  });

  describe('错误处理API', () => {
    it('应该正确处理无效输入', async () => {
      // Arrange - 无效的市场上下文
      const invalidMarketContext = {
        symbol: 'BTC/USDT',
        currentPrice: -1000, // 无效的负价格
        avgPrice: 0,
        volatility: -0.5, // 无效的负波动率
        marketCondition: '',
        timestamp: new Date(),
        dataPoints: 0
      };

      // Act & Assert - 应该优雅处理错误
      await expect(
        predictionEngine.generateMicroPrediction(invalidMarketContext)
      ).resolves.toBeDefined();
    });

    it('应该正确处理数据库错误', async () => {
      // Arrange
      (mockPrisma.learningKnowledgeBase.findFirst as any).mockRejectedValue(new Error('Database error'));

      const timeframe = 'micro' as any;
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      // Act & Assert - 应该优雅处理数据库错误
      await expect(
        learningEngine.getLearningInsights(timeframe, marketContext)
      ).resolves.toBeDefined();
    });
  });

  describe('API响应时间', () => {
    it('所有API应该在合理时间内响应', async () => {
      // Arrange
      const marketContext = {
        symbol: 'BTC/USDT',
        currentPrice: 50000,
        avgPrice: 49000,
        volatility: 0.02,
        marketCondition: 'trending',
        timestamp: new Date(),
        dataPoints: 100
      };

      // Act & Assert - 测试各API响应时间
      const apis = [
        () => predictionEngine.generateMicroPrediction(marketContext),
        () => predictionEngine.generateMesoPrediction(marketContext),
        () => predictionEngine.generateMacroPrediction(marketContext),
        () => systemStarter.getSystemStatus(),
        () => systemStarter.healthCheck(),
        () => systemStarter.getSystemMetrics(),
        () => macroPredictionService.getStatus()
      ];

      for (const api of apis) {
        const startTime = Date.now();
        await api();
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        expect(responseTime).toBeLessThan(5000); // 所有API应该在5秒内响应
      }
    });
  });
});
