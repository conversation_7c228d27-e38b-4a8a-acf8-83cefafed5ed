/**
 * 统一模式识别服务集成测试
 * 重构：测试统一PatternRecognitionService的集成功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Container } from 'inversify';
import { TYPES } from '../../../shared/infrastructure/di/types/index';
import { IPatternRecognitionService } from '../../../shared/infrastructure/analysis/interfaces/IPatternRecognitionService';
import { PatternRecognitionService } from '../../../shared/infrastructure/analysis/services/PatternRecognitionService';
import { KlineDataPoint, PatternType } from '../../../shared/infrastructure/analysis/types/PatternTypes';
import { IBasicLogger } from '../../../shared/infrastructure/logging/interfaces/basic-logger.interface';

describe('统一模式识别服务集成测试', () => {
  let container: Container;
  let patternService: IPatternRecognitionService;
  let mockLogger: IBasicLogger;

  beforeEach(() => {
    mockLogger = {
      debug: vi.fn(),
      info: vi.fn(),
      warn: vi.fn(),
      error: vi.fn()
    };

    // 创建模拟的RealMarketDataService
    const mockMarketDataService = {
      getKlineData: vi.fn().mockResolvedValue([]),
      getLatestPrice: vi.fn().mockResolvedValue(50000),
      subscribeToUpdates: vi.fn(),
      unsubscribeFromUpdates: vi.fn()
    };

    // 设置DI容器
    container = new Container();
    container.bind<IBasicLogger>(TYPES.Logger).toConstantValue(mockLogger);
    container.bind(TYPES.MarketData.RealMarketDataService).toConstantValue(mockMarketDataService);
    container.bind<IPatternRecognitionService>(TYPES.Shared.PatternRecognitionService)
      .to(PatternRecognitionService)
      .inSingletonScope();

    patternService = container.get<IPatternRecognitionService>(TYPES.Shared.PatternRecognitionService);
  });

  describe('统一模式识别功能测试', () => {
    it('应该能识别多种形态类型', async () => {
      const complexKlines = createComplexMarketData();

      const patterns = await patternService.recognize(complexKlines);

      expect(patterns.length).toBeGreaterThan(0);

      // 验证识别的形态有不同类型
      const patternTypes = new Set(patterns.map(p => p.type));
      expect(patternTypes.size).toBeGreaterThan(1);

      // 验证基本形态属性
      patterns.forEach(pattern => {
        expect(pattern.type).toBeDefined();
        expect(pattern.name).toBeDefined();
        expect(pattern.confidence).toBeGreaterThan(0);
        expect(pattern.confidence).toBeLessThanOrEqual(1);
      });
    });

    it('应该保持形态质量标准', async () => {
      const qualityKlines = createHighQualityPatternData();

      const patterns = await patternService.recognize(qualityKlines);

      // 所有识别的形态都应该有合理的置信度
      patterns.forEach(pattern => {
        expect(pattern.confidence).toBeGreaterThan(0.5);
        expect(pattern.confidence).toBeLessThanOrEqual(1.0);
        expect(pattern.completeness).toBeGreaterThan(0.5);
        expect(pattern.completeness).toBeLessThanOrEqual(1.0);
        expect(pattern.clarity).toBeGreaterThan(0.3);
        expect(pattern.clarity).toBeLessThanOrEqual(1.0);
      });
    });

    it('should handle overlapping patterns correctly', async () => {
      const overlappingKlines = createOverlappingPatternData();

      const patterns = await patternService.recognize(overlappingKlines);
      
      // 应该能识别重叠的形态而不冲突
      expect(patterns.length).toBeGreaterThan(0);
      
      // 检查是否有时间重叠的形态
      const timeRanges = patterns.map(p => ({
        start: p.startTime.getTime(),
        end: p.endTime.getTime()
      }));
      
      // 应该允许合理的重叠
      expect(timeRanges.length).toBeGreaterThanOrEqual(1);
    });

    it('should provide comprehensive pattern metadata', async () => {
      const metadataKlines = createMetadataTestData();

      const patterns = await patternService.recognize(metadataKlines);
      
      patterns.forEach(pattern => {
        expect(pattern.type).toBeDefined();
        expect(pattern.name).toBeDefined();
        expect(pattern.keyPoints).toBeDefined();
        expect(pattern.keyPoints.length).toBeGreaterThan(0);
        expect(pattern.measurements).toBeDefined();
        expect(pattern.measurements.height).toBeGreaterThan(0);
        expect(pattern.measurements.width).toBeGreaterThan(0);
        expect(pattern.targets).toBeDefined();
        expect(pattern.targets.length).toBeGreaterThan(0);
        expect(pattern.stopLoss).toBeGreaterThan(0);
        expect(pattern.timeframe).toBe('1h');
        expect(pattern.metadata).toBeDefined();
      });
    });
  });

  describe('Performance Integration Tests', () => {
    it('should process large datasets efficiently across all recognizers', async () => {
      const largeKlines = createLargeIntegratedDataset(1000);
      
      const startTime = Date.now();
      const patterns = await patternService.recognize(largeKlines);
      const endTime = Date.now();
      
      expect(endTime - startTime).toBeLessThan(10000); // 10秒内完成
      expect(patterns).toBeDefined();
      
      // 验证日志记录
      expect(mockLogger.debug).toHaveBeenCalledWith(
        expect.stringContaining('形态识别完成'),
        expect.objectContaining({
          originalPatterns: expect.any(Number),
          uniquePatterns: expect.any(Number)
        })
      );
    });

    it('should maintain memory efficiency during processing', async () => {
      const memoryTestKlines = createMemoryTestDataset(500);
      
      // 多次运行以测试内存泄漏
      for (let i = 0; i < 5; i++) {
        const patterns = await patternService.recognize(memoryTestKlines);
        expect(patterns).toBeDefined();
      }
      
      // 如果有内存泄漏，这里应该会失败或变慢
      expect(true).toBe(true);
    });

    it('should handle concurrent pattern recognition requests', async () => {
      const concurrentKlines = createConcurrentTestData();
      
      // 并发执行多个识别请求
      const promises = Array.from({ length: 3 }, () =>
        patternService.recognize(concurrentKlines)
      );
      
      const results = await Promise.all(promises);
      
      // 所有请求都应该成功完成
      results.forEach(patterns => {
        expect(patterns).toBeDefined();
        expect(Array.isArray(patterns)).toBe(true);
      });
    });
  });

  describe('Error Handling Integration', () => {
    it('should handle errors from individual recognizers gracefully', async () => {
      // 创建可能导致某个识别器出错的数据
      const problematicKlines = createProblematicData();
      
      const patterns = await patternService.recognize(problematicKlines);
      
      // 即使某个识别器出错，整体流程应该继续
      expect(patterns).toBeDefined();
      expect(Array.isArray(patterns)).toBe(true);
    });

    it('should log appropriate error messages', async () => {
      const errorKlines = createErrorInducingData();
      
      await patternService.recognize(errorKlines);
      
      // 应该记录调试信息
      expect(mockLogger.debug).toHaveBeenCalled();
    });

    it('should handle malformed input data', async () => {
      const malformedKlines = [
        { timestamp: null, close: 50000 },
        { timestamp: new Date(), close: null },
        { invalid: 'data' }
      ];
      
      await expect(patternService.recognize(malformedKlines as any)).resolves.not.toThrow();
    });
  });

  describe('Pattern Conversion and Standardization', () => {
    it('should convert harmonic patterns to standard format correctly', async () => {
      const harmonicKlines = createHarmonicTestData();
      
      const patterns = await patternService.recognize(harmonicKlines);
      
      // 验证返回的形态具有正确的结构
      patterns.forEach(pattern => {
        expect(pattern.type).toBeDefined();
        expect(pattern.keyPoints).toBeDefined();
        expect(pattern.keyPoints.length).toBeGreaterThan(0);
        pattern.keyPoints.forEach(point => {
          expect(point.timestamp).toBeDefined();
          expect(point.price).toBeDefined();
          expect(point.type).toBeDefined();
        });
      });
    });

    it('should convert Elliott Wave patterns to standard format correctly', async () => {
      const elliottKlines = createElliottWaveTestData();
      
      const patterns = await patternService.recognize(elliottKlines);
      
      // 验证Elliott Wave形态的基本结构
      patterns.forEach(pattern => {
        expect(pattern.type).toBeDefined();
        expect(pattern.confidence).toBeDefined();
        expect(pattern.startTime).toBeDefined();
        expect(pattern.endTime).toBeDefined();
      });
    });

    it('should convert Gann patterns to standard format correctly', async () => {
      const gannKlines = createGannTestData();
      
      const patterns = await patternService.recognize(gannKlines);
      
      const gannPatterns = patterns.filter(p => p.category === 'GANN');
      gannPatterns.forEach(pattern => {
        expect(pattern.measurements.symmetry).toBeDefined();
        expect(pattern.measurements.symmetry).toBeGreaterThanOrEqual(0.5); // 默认值或计算值
      });
    });
  });

  describe('Classic Pattern Integration', () => {
    it('should identify classic patterns as supplementary', async () => {
      const classicKlines = createClassicPatternData();
      
      const patterns = await patternService.recognize(classicKlines);
      
      const classicPatterns = patterns.filter(p => p.category === 'CLASSIC');
      expect(classicPatterns.length).toBeGreaterThanOrEqual(0);
      
      // 检查经典形态类型
      const patternTypes = classicPatterns.map(p => p.type);
      const expectedTypes = ['HEAD_AND_SHOULDERS_TOP', 'DOUBLE_TOP', 'ASCENDING_TRIANGLE'];
      
      patternTypes.forEach(type => {
        expect(expectedTypes.some(expected => type.includes(expected.split('_')[0]))).toBe(true);
      });
    });

    it('should validate classic pattern measurements', async () => {
      const classicKlines = createClassicPatternData();
      
      const patterns = await patternService.recognize(classicKlines);
      
      const classicPatterns = patterns.filter(p => p.category === 'CLASSIC');
      classicPatterns.forEach(pattern => {
        expect(pattern.measurements.height).toBeGreaterThan(0);
        expect(pattern.measurements.width).toBeGreaterThan(0);
        expect(pattern.measurements.symmetry).toBeGreaterThanOrEqual(0.5);
        expect(pattern.measurements.symmetry).toBeLessThanOrEqual(1.0);
      });
    });
  });
});

// 辅助函数：创建测试数据
function createComplexMarketData() {
  const klines = [];
  const basePrice = 50000;
  
  // 创建包含多种形态的复杂市场数据
  for (let i = 0; i < 200; i++) {
    let price = basePrice;
    
    // 添加趋势
    price += i * 5;
    
    // 添加周期性波动（可能形成艾略特波浪）
    price += Math.sin(i / 20) * 800;
    
    // 添加谐波成分
    price += Math.sin(i / 13) * 300;
    
    // 添加江恩角度线成分
    if (i > 50) {
      price += (i - 50) * 2; // 1x2角度线成分
    }
    
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 50,
      high: price + 100,
      low: price - 100,
      close: price,
      volume: 1000 + Math.random() * 500
    });
  }
  
  return klines;
}

function createHighQualityPatternData() {
  // 创建高质量、清晰的形态数据
  return createComplexMarketData();
}

function createOverlappingPatternData() {
  // 创建可能产生重叠形态的数据
  const klines = [];
  const basePrice = 50000;
  
  for (let i = 0; i < 150; i++) {
    const price = basePrice + Math.sin(i / 15) * 1000 + Math.cos(i / 25) * 500;
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 30,
      high: price + 80,
      low: price - 80,
      close: price,
      volume: 1000
    });
  }
  
  return klines;
}

function createMetadataTestData() {
  return createComplexMarketData();
}

function createLargeIntegratedDataset(count: number) {
  const klines = [];
  const basePrice = 50000;
  
  for (let i = 0; i < count; i++) {
    const price = basePrice + Math.sin(i / 50) * 2000 + Math.cos(i / 100) * 1000 + i * 2;
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 100,
      high: price + 200,
      low: price - 200,
      close: price,
      volume: 1000 + Math.random() * 1000
    });
  }
  
  return klines;
}

function createMemoryTestDataset(count: number) {
  return createLargeIntegratedDataset(count);
}

function createConcurrentTestData() {
  return createComplexMarketData();
}

function createProblematicData() {
  // 创建可能导致识别器出错的边界情况数据
  const klines = [];
  
  for (let i = 0; i < 50; i++) {
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: 50000,
      high: 50000,
      low: 50000,
      close: 50000,
      volume: 0
    });
  }
  
  return klines;
}

function createErrorInducingData() {
  return [
    {
      timestamp: new Date(),
      open: 50000,
      high: 50100,
      low: 49900,
      close: 50000,
      volume: 1000
    }
  ];
}

function createHarmonicTestData() {
  const klines = [];
  const basePrice = 50000;
  
  // 创建Gartley形态数据
  for (let i = 0; i < 60; i++) {
    let price = basePrice;
    
    if (i < 10) price = basePrice + 1000; // X点
    else if (i < 20) price = basePrice; // A点
    else if (i < 30) price = basePrice + 618; // B点 (61.8% retracement)
    else if (i < 40) price = basePrice + 200; // C点
    else price = basePrice + 500; // D点
    
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 50,
      high: price + 100,
      low: price - 100,
      close: price,
      volume: 1000
    });
  }
  
  return klines;
}

function createElliottWaveTestData() {
  const klines = [];
  const basePrice = 50000;
  
  // 创建5波推动结构
  for (let i = 0; i < 80; i++) {
    let price = basePrice;
    
    if (i < 15) price = basePrice + i * 50; // 波浪1
    else if (i < 25) price = basePrice + 750 - (i - 15) * 30; // 波浪2
    else if (i < 45) price = basePrice + 450 + (i - 25) * 80; // 波浪3
    else if (i < 55) price = basePrice + 2050 - (i - 45) * 40; // 波浪4
    else price = basePrice + 1650 + (i - 55) * 60; // 波浪5
    
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 50,
      high: price + 100,
      low: price - 100,
      close: price,
      volume: 1000
    });
  }
  
  return klines;
}

function createGannTestData() {
  const klines = [];
  const basePrice = 50000;
  
  // 创建1x1江恩角度线数据
  for (let i = 0; i < 60; i++) {
    const price = basePrice + i * 10; // 1:1比例
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 20,
      high: price + 30,
      low: price - 30,
      close: price,
      volume: 1000
    });
  }
  
  return klines;
}

function createClassicPatternData() {
  const klines = [];
  const basePrice = 50000;
  
  // 创建头肩顶形态
  for (let i = 0; i < 60; i++) {
    let price = basePrice;
    
    if (i < 10) price = basePrice + 500; // 左肩
    else if (i < 20) price = basePrice + 200; // 颈线
    else if (i < 30) price = basePrice + 800; // 头部
    else if (i < 40) price = basePrice + 200; // 颈线
    else if (i < 50) price = basePrice + 500; // 右肩
    else price = basePrice + 100; // 突破
    
    klines.push({
      timestamp: new Date(Date.now() + i * 60000),
      open: price - 50,
      high: price + 100,
      low: price - 100,
      close: price,
      volume: 1000
    });
  }
  
  return klines;
}
