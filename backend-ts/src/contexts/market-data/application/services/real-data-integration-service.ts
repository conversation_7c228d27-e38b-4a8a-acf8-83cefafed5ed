/**
 * 真实数据集成服务
 * 整合所有外部API数据源，实现真正的四维度信号融合
 */

import { Logger } from 'winston';
import { injectable, inject } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { UNIFIED_TECHNICAL_INDICATOR_TYPES } from '../../../../shared/infrastructure/technical-indicators/types';
// 修复分层架构违规：Application层通过Domain层接口访问Infrastructure层
import { IMempoolAdapter } from '../../domain/services/external-adapters/IMempoolAdapter';
import { IFearGreedAdapter } from '../../domain/services/external-adapters/IFearGreedAdapter';
import { IBinanceFuturesAdapter } from '../../domain/services/external-adapters/IBinanceFuturesAdapter';
import { ICoinMetricsAdapter } from '../../domain/services/external-adapters/ICoinMetricsAdapter';
import { ISentiCryptAdapter } from '../../domain/services/external-adapters/ISentiCryptAdapter';
import { IDynamicWeightingService } from '../../../../shared/infrastructure/analysis/interfaces/IDynamicWeightingService';
// 定义接口避免跨Context依赖，通过DI容器注入实现
export interface IConfidenceCalculator {
  calculateConfidence(data: any): Promise<number>;
  getConfidenceMetrics(): Promise<any>;
}
import { IHistoricalDataRepository } from '../../domain/repositories/historical-data-repository';
import {
  IUnifiedTechnicalIndicatorCalculator
} from '../../../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';

export interface IntegratedMarketData {
  symbol: string;
  timestamp: Date;
  
  // 技术面数据 (来自历史价格数据)
  technical: {
    price: number;
    rsi: number;
    macd: {
      macd: number;
      signal: number;
      histogram: number;
    };
    bollinger: {
      upper: number;
      middle: number;
      lower: number;
    };
    adx: number;
    dataSource: 'HISTORICAL_PRICE_DATA' | 'REAL_HISTORICAL_DATA' | 'EXTERNAL_API' | 'REALTIME_PRICE' | 'DEFAULT_VALUES' | 'FALLBACK_DEFAULT' | 'REPOSITORY_DATA';
    reliability: number;
  };
  
  // 基本面数据 (来自Coin Metrics + 综合分析)
  fundamental: {
    // Coin Metrics链上数据
    onchain: {
      activeAddresses: number;
      transactionCount: number;
      networkValue: number;
      mvrvRatio: number;
      exchangeInflow: number;
      exchangeOutflow: number;
      dataSource: 'COIN_METRICS';
      reliability: number;
    };
    // 综合专业分析 (基于链上数据和网络状态)
    professional: {
      fundamentalScore: number;
      valueAssessment: string;
      category: string;
      dataSource: 'INTEGRATED_ANALYSIS' | 'TOKEN_METRICS';
      reliability: number;
    };
    // 网络健康度 (来自Mempool)
    network: {
      congestionLevel: string;
      feeTrend: string;
      mempoolCount: number;
      dataSource: 'MEMPOOL_SPACE';
      reliability: number;
    };
  };
  
  // 情绪面数据 (来自Fear&Greed + SentiCrypt)
  sentiment: {
    // 市场情绪
    marketSentiment: {
      fearGreedIndex: number;
      sentimentLevel: string;
      marketPhase: string;
      dataSource: 'FEAR_GREED_INDEX';
      reliability: number;
    };
    // 专业情绪分析
    professionalSentiment: {
      sentimentScore: number;
      sentimentLevel: string;
      dataSource: 'SENTICRYPT' | 'TOKEN_METRICS';
      reliability: number;
    };
  };
  
  // 量化面数据 (来自Binance Futures)
  quantitative: {
    derivatives: {
      fundingRate: number;
      fundingRateAnnualized: number;
      openInterest: number;
      longShortRatio: number;
      fundingSentiment: string;
      dataSource: 'BINANCE_FUTURES';
      reliability: number;
    };
  };
  
  // 综合评分
  overallAssessment: {
    technicalScore: number;
    fundamentalScore: number;
    sentimentScore: number;
    quantitativeScore: number;
    combinedScore: number;
    signalStrength: 'WEAK' | 'MODERATE' | 'STRONG';
    recommendation: 'BUY' | 'SELL' | 'HOLD';
    confidence: number;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  };
  
  // 数据质量指标
  dataQuality: {
    technicalQuality: number;
    fundamentalQuality: number;
    sentimentQuality: number;
    quantitativeQuality: number;
    overallQuality: number;
    missingSources: string[];
    dataFreshness: number; // 小时
  };
}

@injectable()
export class RealDataIntegrationService {
  private readonly logger: Logger;
  // 修复分层架构违规：使用Domain层接口而不是Infrastructure层具体实现
  private readonly mempoolAdapter: IMempoolAdapter;
  private readonly fearGreedAdapter: IFearGreedAdapter;
  private readonly binanceFuturesAdapter: IBinanceFuturesAdapter;
  private readonly coinMetricsAdapter: ICoinMetricsAdapter;
  private readonly sentiCryptAdapter: ISentiCryptAdapter;
  private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator;
  private readonly dynamicWeightingService: IDynamicWeightingService;
  private readonly confidenceCalculator: IConfidenceCalculator;
  private readonly historicalDataRepository?: IHistoricalDataRepository;

  constructor(
    @inject(TYPES.Logger) logger: Logger,
    // 修复分层架构违规：通过依赖注入获取接口实现
    @inject(TYPES.MarketData.MempoolAdapter) mempoolAdapter: IMempoolAdapter,
    @inject(TYPES.MarketData.FearGreedAdapter) fearGreedAdapter: IFearGreedAdapter,
    @inject(TYPES.MarketData.BinanceFuturesAdapter) binanceFuturesAdapter: IBinanceFuturesAdapter,
    @inject(TYPES.MarketData.CoinMetricsAdapter) coinMetricsAdapter: ICoinMetricsAdapter,
    @inject(TYPES.MarketData.SentiCryptAdapter) sentiCryptAdapter: ISentiCryptAdapter,
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator) technicalCalculator: IUnifiedTechnicalIndicatorCalculator,
    @inject(TYPES.Shared.DynamicWeightingService) dynamicWeightingService: IDynamicWeightingService,
    @inject(TYPES.Shared.ConfidenceCalculator) confidenceCalculator: IConfidenceCalculator,
    @inject(TYPES.MarketData.HistoricalDataRepository) historicalDataRepository?: IHistoricalDataRepository
  ) {
    this.logger = logger;
    this.mempoolAdapter = mempoolAdapter;
    this.fearGreedAdapter = fearGreedAdapter;
    this.binanceFuturesAdapter = binanceFuturesAdapter;
    this.coinMetricsAdapter = coinMetricsAdapter;
    this.sentiCryptAdapter = sentiCryptAdapter;
    this.technicalCalculator = technicalCalculator;
    this.dynamicWeightingService = dynamicWeightingService;
    this.confidenceCalculator = confidenceCalculator;
    this.historicalDataRepository = historicalDataRepository;

    this.logger.info('真实数据集成服务初始化 - 使用统一DynamicWeightingService');
  }

  /**
   * 获取BTC的完整集成数据
   */
  async getBTCIntegratedData(): Promise<IntegratedMarketData> {
    this.logger.info('开始获取BTC完整集成数据');
    
    const symbol = 'BTC';
    const timestamp = new Date();
    
    // 并行获取所有数据源
    const [
      technicalData,
      fundamentalData,
      sentimentData,
      quantitativeData
    ] = await Promise.allSettled([
      this.getTechnicalData(symbol),
      this.getFundamentalData(symbol),
      this.getSentimentData(symbol),
      this.getQuantitativeData(symbol)
    ]);

    // 处理技术面数据
    if (technicalData.status !== 'fulfilled') {
      throw new Error(`技术面数据获取失败: ${technicalData.reason}。请检查技术指标计算服务配置。`);
    }
    const technical = technicalData.value;

    // 处理基本面数据
    if (fundamentalData.status !== 'fulfilled') {
      throw new Error(`基本面数据获取失败: ${fundamentalData.reason}。请检查链上数据源和专业分析服务配置。`);
    }
    const fundamental = fundamentalData.value;

    // 处理情绪面数据
    if (sentimentData.status !== 'fulfilled') {
      throw new Error(`情绪面数据获取失败: ${sentimentData.reason}。请检查恐慌贪婪指数和情绪分析服务配置。`);
    }
    const sentiment = sentimentData.value;

    // 处理量化面数据
    if (quantitativeData.status !== 'fulfilled') {
      throw new Error(`量化面数据获取失败: ${quantitativeData.reason}。请检查衍生品数据和量化分析服务配置。`);
    }
    const quantitative = quantitativeData.value;

    // 计算综合评分
    const overallAssessment = await this.calculateOverallAssessment(
      technical, fundamental, sentiment, quantitative
    );

    // 计算数据质量
    const dataQuality = this.calculateDataQuality(
      technical, fundamental, sentiment, quantitative
    );

    const integratedData: IntegratedMarketData = {
      symbol,
      timestamp,
      technical,
      fundamental,
      sentiment,
      quantitative,
      overallAssessment: overallAssessment,
      dataQuality: dataQuality
    };

    this.logger.info('BTC集成数据获取完成', {
      overallScore: overallAssessment.combinedScore,
      dataQuality: dataQuality.overallQuality,
      recommendation: overallAssessment.recommendation
    });

    return integratedData;
  }

  /**
   * 获取技术面数据 - 使用真实的历史数据计算，增强错误处理
   */
  private async getTechnicalData(symbol: string): Promise<IntegratedMarketData['technical']> {
    try {
      // 首先尝试从数据库获取历史数据
      const result = await this.getHistoricalDataFromRepository(symbol);
      if (result) {
        this.logger.info('技术数据获取成功，使用数据库数据', { symbol });
        return result;
      }

      // 如果数据库没有数据，尝试从外部API获取
      const externalResult = await this.getHistoricalDataFromExternalAPI(symbol);
      if (externalResult) {
        this.logger.info('技术数据获取成功，使用外部API数据', { symbol });
        return externalResult;
      }

      // 如果都失败，抛出错误
      throw new Error(`无法获取 ${symbol} 的技术数据`);
    } catch (error) {
      this.logger.error('技术数据获取失败', {
        symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }

  }

  /**
   * 策略1：从历史数据仓储获取数据
   */
  private async getHistoricalDataFromRepository(symbol: string): Promise<IntegratedMarketData['technical'] | null> {
    if (!this.historicalDataRepository) {
      throw new Error('历史数据仓储未配置');
    }

    const historicalData = await this.historicalDataRepository.findByTimeRange(
      new (await import('../../domain/value-objects/trading-symbol')).TradingSymbol(symbol),
      new (await import('../../domain/value-objects/timeframe')).Timeframe('1h'),
      new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30天前
      new Date()
    );

    return this.processHistoricalData(historicalData, 'REPOSITORY_DATA', 0.95);
  }

  /**
   * 策略2：从外部API获取历史数据
   */
  private async getHistoricalDataFromExternalAPI(symbol: string): Promise<IntegratedMarketData['technical'] | null> {
    try {
      this.logger.info('开始从外部API获取历史数据', { symbol });

      // 使用Binance Futures API获取历史K线数据
      const klineData = await this.binanceFuturesAdapter.getFuturesKlines(
        symbol.toUpperCase() + 'USDT', // 确保符号格式正确
        '1h', // 1小时K线
        100   // 获取最近100根K线
      );

      if (!klineData || klineData.length === 0) {
        this.logger.warn('外部API返回空数据', { symbol });
        return null;
      }

      // 转换K线数据为技术分析所需的格式
      const priceData = klineData.map(kline => ({
        timestamp: kline.openTime,
        open: kline.open,
        high: kline.high,
        low: kline.low,
        close: kline.close,
        volume: kline.volume
      }));

      // 使用技术指标计算器计算技术指标
      const technicalIndicators = await this.technicalCalculator.calculateBatch(priceData, {
        indicators: ['SMA', 'EMA', 'RSI', 'MACD', 'BOLLINGER_BANDS'],
        periods: { SMA: 20, EMA: 12, RSI: 14 }
      });

      // 构建技术面数据
      const technicalData: IntegratedMarketData['technical'] = {
        priceAction: {
          currentPrice: priceData[priceData.length - 1].close,
          priceChange24h: this.calculatePriceChange(priceData),
          priceChangePercent24h: this.calculatePriceChangePercent(priceData),
          high24h: Math.max(...priceData.slice(-24).map(p => p.high)),
          low24h: Math.min(...priceData.slice(-24).map(p => p.low)),
          volume24h: priceData.slice(-24).reduce((sum, p) => sum + p.volume, 0),
          dataSource: 'BINANCE_FUTURES',
          reliability: 0.95
        },
        indicators: {
          sma20: technicalIndicators.SMA?.[technicalIndicators.SMA.length - 1] || 0,
          ema12: technicalIndicators.EMA?.[technicalIndicators.EMA.length - 1] || 0,
          rsi14: technicalIndicators.RSI?.[technicalIndicators.RSI.length - 1] || 50,
          macd: {
            macd: technicalIndicators.MACD?.macd?.[technicalIndicators.MACD.macd.length - 1] || 0,
            signal: technicalIndicators.MACD?.signal?.[technicalIndicators.MACD.signal.length - 1] || 0,
            histogram: technicalIndicators.MACD?.histogram?.[technicalIndicators.MACD.histogram.length - 1] || 0
          },
          bollingerBands: {
            upper: technicalIndicators.BOLLINGER_BANDS?.upper?.[technicalIndicators.BOLLINGER_BANDS.upper.length - 1] || 0,
            middle: technicalIndicators.BOLLINGER_BANDS?.middle?.[technicalIndicators.BOLLINGER_BANDS.middle.length - 1] || 0,
            lower: technicalIndicators.BOLLINGER_BANDS?.lower?.[technicalIndicators.BOLLINGER_BANDS.lower.length - 1] || 0
          },
          dataSource: 'BINANCE_FUTURES',
          reliability: 0.9
        }
      };

      this.logger.info('外部API历史数据获取成功', {
        symbol,
        dataPoints: priceData.length,
        currentPrice: technicalData.priceAction.currentPrice
      });

      return technicalData;
    } catch (error) {
      this.logger.error('外部API获取历史数据失败', { error: error instanceof Error ? error.message : String(error), symbol });
      return null;
    }
  }

  /**
   * 计算24小时价格变化
   */
  private calculatePriceChange(priceData: Array<{close: number}>): number {
    if (priceData.length < 24) return 0;
    const current = priceData[priceData.length - 1].close;
    const previous = priceData[priceData.length - 24].close;
    return current - previous;
  }

  /**
   * 计算24小时价格变化百分比
   */
  private calculatePriceChangePercent(priceData: Array<{close: number}>): number {
    if (priceData.length < 24) return 0;
    const current = priceData[priceData.length - 1].close;
    const previous = priceData[priceData.length - 24].close;
    return previous > 0 ? ((current - previous) / previous) * 100 : 0;
  }

  /**
   * 策略3：使用真实的实时价格数据生成基础技术指标
   */
  private async getRealtimePriceData(symbol: string): Promise<IntegratedMarketData['technical'] | null> {
    try {
      // 🔥 获取真实的BTC价格和技术指标 - 零容忍假数据
      const binanceSymbol = symbol === 'BTC' ? 'BTCUSDT' : `${symbol}USDT`;

      // 获取当前价格
      const priceResponse = await fetch(`https://api.binance.com/api/v3/ticker/price?symbol=${binanceSymbol}`);
      const priceData = await priceResponse.json() as { price: string };
      const currentPrice = parseFloat(priceData.price);

      if (!currentPrice || currentPrice <= 0) {
        this.logger.error('获取到无效的价格数据', { symbol, price: currentPrice });
        return null;
      }

      // 获取K线数据计算技术指标
      const klineResponse = await fetch(`https://api.binance.com/api/v3/klines?symbol=${binanceSymbol}&interval=1h&limit=100`);
      const klineData = await klineResponse.json() as any[];

      if (!klineData || !Array.isArray(klineData) || klineData.length < 50) {
        this.logger.warn('K线数据不足，无法计算技术指标', { symbol, dataLength: klineData?.length });
        return {
          price: currentPrice,
          rsi: null,
          macd: null,
          bollinger: null,
          adx: null,
          dataSource: 'REALTIME_PRICE',
          reliability: 0.5
        };
      }

      // 计算真实的技术指标 - 使用统一技术指标计算器
      const closes = klineData.map((k: any) => parseFloat(k[4]));
      const highs = klineData.map((k: any) => parseFloat(k[2]));
      const lows = klineData.map((k: any) => parseFloat(k[3]));

      const rsiResult = this.technicalCalculator.calculateRSI(closes, 14);
      const macdResult = this.technicalCalculator.calculateMACD(closes);
      const bollingerResult = this.technicalCalculator.calculateBollingerBands(closes, 20, 2);
      const adxResult = this.technicalCalculator.calculateADX(highs, lows, closes, 14);
      const adx = adxResult.value;

      return {
        price: currentPrice,
        rsi: rsiResult.value,
        macd: {
          macd: macdResult.macd,
          signal: macdResult.signal,
          histogram: macdResult.histogram
        },
        bollinger: {
          upper: bollingerResult.upper,
          middle: bollingerResult.middle,
          lower: bollingerResult.lower
        },
        adx,
        dataSource: 'EXTERNAL_API',
        reliability: 0.95
      };
    } catch (error) {
      this.logger.error('获取真实价格数据失败', { error, symbol });
      return null;
    }
  }

  /**
   * 处理历史数据并计算技术指标
   */
  private processHistoricalData(
    historicalData: any[],
    dataSource: 'HISTORICAL_PRICE_DATA' | 'REAL_HISTORICAL_DATA' | 'EXTERNAL_API' | 'REALTIME_PRICE' | 'DEFAULT_VALUES' | 'FALLBACK_DEFAULT' | 'REPOSITORY_DATA',
    baseReliability: number
  ): IntegratedMarketData['technical'] | null {
    if (!historicalData || historicalData.length === 0) {
      return null;
    }

    // 数据质量验证
    const dataQuality = this.validateDataQuality(historicalData);
    if (dataQuality.score < 0.5) {
      this.logger.warn('数据质量不足', {
        dataQuality,
        dataSource,
        dataPoints: historicalData.length
      });
      return null;
    }

    try {
      const prices = historicalData.map(d => d.closePrice);
      const latestPrice = prices[prices.length - 1];

      // 计算技术指标 - 使用统一技术指标计算器
      const rsiResult = this.technicalCalculator.calculateRSI(prices, 14);
      const macdResult = this.technicalCalculator.calculateMACD(prices);
      const bollingerResult = this.technicalCalculator.calculateBollingerBands(prices, 20);
      const highs = historicalData.map((d: any) => d.highPrice);
      const lows = historicalData.map((d: any) => d.lowPrice);
      const closes = historicalData.map((d: any) => d.closePrice);
      const adxResult = this.technicalCalculator.calculateADX(highs, lows, closes, 14);
      const adx = adxResult.value;

      // 根据数据质量调整可靠性
      const adjustedReliability = baseReliability * dataQuality.score;

      return {
        price: latestPrice,
        rsi: rsiResult.value,
        macd: {
          macd: macdResult.macd,
          signal: macdResult.signal,
          histogram: macdResult.histogram
        },
        bollinger: {
          upper: bollingerResult.upper,
          middle: bollingerResult.middle,
          lower: bollingerResult.lower
        },
        adx: adx || 25,
        dataSource,
        reliability: adjustedReliability
      };
    } catch (error) {
      this.logger.error('技术指标计算失败', { error, dataSource });
      return null;
    }
  }

  /**
   * 验证数据质量
   */
  private validateDataQuality(data: any[]): { score: number; issues: string[] } {
    const issues: string[] = [];
    let score = 1.0;

    // 检查数据量
    if (data.length < 50) {
      issues.push('数据点不足50个');
      score -= 0.3;
    }

    // 检查数据完整性
    const missingData = data.filter(d =>
      !d.closePrice || !d.highPrice || !d.lowPrice || !d.openPrice
    ).length;

    if (missingData > 0) {
      issues.push(`${missingData}个数据点缺失价格信息`);
      score -= (missingData / data.length) * 0.5;
    }

    // 检查数据异常值
    const prices = data.map(d => d.closePrice).filter(p => p > 0);
    if (prices.length > 0) {
      const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length;
      const outliers = prices.filter(p =>
        Math.abs(p - avgPrice) > avgPrice * 0.5
      ).length;

      if (outliers > data.length * 0.1) {
        issues.push(`${outliers}个异常价格点`);
        score -= 0.2;
      }
    }

    return { score: Math.max(0, score), issues };
  }


  /**
   * 获取基本面数据 - 移除TokenMetrics依赖，使用综合数据源
   */
  private async getFundamentalData(symbol: string): Promise<IntegratedMarketData['fundamental']> {
    // 获取Coin Metrics链上数据
    const onchainData = await this.coinMetricsAdapter.getBTCOnChainData(1);
    const latestOnchain = onchainData.length > 0 ? onchainData[onchainData.length - 1] : null;

    // 获取网络状态
    const networkStats = await this.mempoolAdapter.getNetworkStats();

    // 基于链上数据和网络状态计算专业基本面分析
    const professionalAnalysis = this.calculateProfessionalFundamentalAnalysis(
      latestOnchain,
      networkStats
    );

    return {
      onchain: {
        activeAddresses: latestOnchain?.activeAddresses || 0,
        transactionCount: latestOnchain?.transactionCount || 0,
        networkValue: latestOnchain?.networkValue || 0,
        mvrvRatio: latestOnchain?.mvrvRatio || 0,
        exchangeInflow: latestOnchain?.exchangeInflow || 0,
        exchangeOutflow: latestOnchain?.exchangeOutflow || 0,
        dataSource: 'COIN_METRICS',
        reliability: latestOnchain?.dataQuality || 0.8
      },
      professional: {
        fundamentalScore: professionalAnalysis.score,
        valueAssessment: professionalAnalysis.valueAssessment,
        category: 'Digital Currency',
        dataSource: 'INTEGRATED_ANALYSIS',
        reliability: professionalAnalysis.reliability
      },
      network: {
        congestionLevel: networkStats.congestionLevel,
        feeTrend: this.calculateFeeTrend(networkStats),
        mempoolCount: networkStats.mempoolCount,
        dataSource: 'MEMPOOL_SPACE',
        reliability: 0.9
      }
    };
  }

  /**
   * 基于链上数据和网络状态计算专业基本面分析
   */
  private calculateProfessionalFundamentalAnalysis(
    onchainData: any,
    networkStats: any
  ): { score: number; valueAssessment: string; reliability: number } {
    let score = 50; // 基础中性评分
    let reliability = 0.6;

    if (onchainData) {
      // MVRV比率分析
      if (onchainData.mvrvRatio) {
        if (onchainData.mvrvRatio < 1) {
          score += 15; // 可能被低估
        } else if (onchainData.mvrvRatio > 3) {
          score -= 15; // 可能被高估
        }
      }

      // 网络价值分析
      if (onchainData.networkValue > 0) {
        score += 10; // 有网络价值
        reliability += 0.1;
      }

      // 交易所流入流出分析
      if (onchainData.exchangeInflow && onchainData.exchangeOutflow) {
        const netFlow = onchainData.exchangeOutflow - onchainData.exchangeInflow;
        if (netFlow > 0) {
          score += 10; // 净流出，可能看涨
        } else if (netFlow < 0) {
          score -= 5; // 净流入，可能看跌
        }
        reliability += 0.1;
      }

      // 活跃地址分析
      if (onchainData.activeAddresses > 0) {
        score += 5; // 网络活跃
        reliability += 0.05;
      }
    }

    // 网络状态分析
    if (networkStats) {
      if (networkStats.congestionLevel === 'LOW') {
        score += 5; // 网络健康
      } else if (networkStats.congestionLevel === 'HIGH') {
        score -= 10; // 网络拥堵
      }
      reliability += 0.1;
    }

    // 确定价值评估
    let valueAssessment: string;
    if (score >= 70) {
      valueAssessment = 'UNDERVALUED';
    } else if (score <= 30) {
      valueAssessment = 'OVERVALUED';
    } else {
      valueAssessment = 'FAIR';
    }

    return {
      score: Math.max(0, Math.min(100, score)),
      valueAssessment,
      reliability: Math.min(0.9, reliability)
    };
  }

  /**
   * 计算费用趋势
   */
  private calculateFeeTrend(networkStats: any): string {
    // 基于网络拥堵程度推断费用趋势
    if (networkStats.congestionLevel === 'HIGH') {
      return 'RISING';
    } else if (networkStats.congestionLevel === 'LOW') {
      return 'FALLING';
    } else {
      return 'STABLE';
    }
  }

  /**
   * 获取情绪面数据 - 使用V3.0统一策略模式
   */
  private async getSentimentData(symbol: string): Promise<IntegratedMarketData['sentiment']> {
    try {
      // 使用V3.0情绪分析策略获取统一的情绪数据
      const { SentimentAnalysisModule } = await import('../../../trend-analysis/infrastructure/modules/sentiment/sentiment-analysis-module');
      const { TradingSymbol } = await import('../../domain/value-objects/trading-symbol');

      // 创建情绪分析模块实例 - 使用简化的依赖创建
      // 注意：这是临时解决方案，在生产环境中应该通过DI容器注入
      const dataProcessingPipeline = null; // 暂时设为null，避免复杂的依赖注入
      const strategyManager = null; // 暂时设为null，避免复杂的依赖注入

      const sentimentModule = new SentimentAnalysisModule(
        this.logger,
        this.sentiCryptAdapter,
        this.fearGreedAdapter,
        dataProcessingPipeline,
        strategyManager
      );

      // 使用V3.0策略模式进行情绪分析
      const sentimentResult = await sentimentModule.analyzeSentiment({
        symbol: new TradingSymbol(symbol),
        timeframe: '1h',
        includeSocial: true,
        includeFearGreed: true,
        includeNews: false,
        includeCommunity: false
      });

      // 转换为IntegratedMarketData格式
      return {
        marketSentiment: {
          fearGreedIndex: sentimentResult.components.fearGreed.index,
          sentimentLevel: sentimentResult.components.fearGreed.level,
          marketPhase: this.determinateMarketPhase(sentimentResult.components.fearGreed.index),
          dataSource: 'FEAR_GREED_INDEX',
          reliability: sentimentResult.components.fearGreed.confidence
        },
        professionalSentiment: {
          sentimentScore: sentimentResult.components.social.score * 100, // 转换为0-100范围
          sentimentLevel: sentimentResult.components.social.signal,
          dataSource: 'SENTICRYPT',
          reliability: sentimentResult.components.social.confidence
        }
      };
    } catch (error) {
      this.logger.error('V3.0情绪数据获取失败，使用备用方案', { error, symbol });

      // 备用方案：直接调用适配器（保持兼容性）
      const [fearGreedData, sentiCryptData] = await Promise.allSettled([
        this.fearGreedAdapter.getCurrentIndex(),
        this.sentiCryptAdapter.getTodaySentiment()
      ]);

      const fearGreed = fearGreedData.status === 'fulfilled' ? fearGreedData.value : { index: 50, level: 'NEUTRAL' };
      const sentiCrypt = sentiCryptData.status === 'fulfilled' ? sentiCryptData.value : { sentimentScore: 0, sentimentLevel: 'NEUTRAL', dataQuality: 0.5 };

      return {
        marketSentiment: {
          fearGreedIndex: fearGreed.index,
          sentimentLevel: fearGreed.level,
          marketPhase: this.determinateMarketPhase(fearGreed.index),
          dataSource: 'FEAR_GREED_INDEX',
          reliability: 0.85
        },
        professionalSentiment: {
          sentimentScore: sentiCrypt.sentimentScore,
          sentimentLevel: sentiCrypt.sentimentLevel,
          dataSource: 'SENTICRYPT',
          reliability: sentiCrypt.dataQuality
        }
      };
    }
  }

  /**
   * 根据恐惧贪婪指数确定市场阶段
   */
  private determinateMarketPhase(index: number): string {
    if (index <= 20) return 'EXTREME_FEAR_PHASE';
    if (index <= 40) return 'FEAR_PHASE';
    if (index <= 60) return 'NEUTRAL_PHASE';
    if (index <= 80) return 'GREED_PHASE';
    return 'EXTREME_GREED_PHASE';
  }

  /**
   * 获取量化面数据
   */
  private async getQuantitativeData(symbol: string): Promise<IntegratedMarketData['quantitative']> {
    // 获取Binance Futures数据
    const derivativesData = await this.binanceFuturesAdapter.getComprehensiveDerivativesData();

    return {
      derivatives: {
        fundingRate: derivativesData.fundingRate,
        fundingRateAnnualized: derivativesData.fundingRate8hAnnualized,
        openInterest: derivativesData.openInterest,
        longShortRatio: derivativesData.longShortRatio,
        fundingSentiment: derivativesData.fundingSentiment,
        dataSource: 'BINANCE_FUTURES',
        reliability: 0.9
      }
    };
  }

  /**
   * 计算综合评分 (使用统一的DynamicWeightingService)
   * 重构：移除对OptimizedSignalWeights的依赖
   */
  private async calculateOverallAssessment(
    technical: IntegratedMarketData['technical'],
    fundamental: IntegratedMarketData['fundamental'],
    sentiment: IntegratedMarketData['sentiment'],
    quantitative: IntegratedMarketData['quantitative']
  ): Promise<IntegratedMarketData['overallAssessment']> {
    // 技术面评分 (0-100)
    const technicalScore = this.calculateTechnicalScore(technical);

    // 基本面评分 (0-100)
    const fundamentalScore = this.calculateFundamentalScore(fundamental);

    // 情绪面评分 (0-100)
    const sentimentScore = this.calculateSentimentScore(sentiment);

    // 量化面评分 (0-100)
    const quantitativeScore = this.calculateQuantitativeScore(quantitative);

    // 计算数据质量
    const dataQuality = {
      technicalQuality: technical.reliability,
      fundamentalQuality: (
        fundamental.onchain.reliability +
        fundamental.professional.reliability +
        fundamental.network.reliability
      ) / 3,
      sentimentQuality: (
        sentiment.marketSentiment.reliability +
        sentiment.professionalSentiment.reliability
      ) / 2,
      quantitativeQuality: quantitative.derivatives.reliability,
      overallQuality: 0 // 将在下面计算
    };

    dataQuality.overallQuality = (
      dataQuality.technicalQuality +
      dataQuality.fundamentalQuality +
      dataQuality.sentimentQuality +
      dataQuality.quantitativeQuality
    ) / 4;

    // 使用统一的DynamicWeightingService进行权重分配
    let combinedScore: number;
    let optimizedWeights: { technical: number; fundamental: number; sentiment: number; quantitative: number };

    try {
      const currentTime = new Date();
      const trends = [
        {
          timeframe: 'technical',
          strength: dataQuality.technicalQuality * 10,
          confidence: dataQuality.technicalQuality,
          direction: 'UP' as const,
          volume: 1000,
          momentum: 1,
          timestamp: currentTime
        },
        {
          timeframe: 'fundamental',
          strength: dataQuality.fundamentalQuality * 10,
          confidence: dataQuality.fundamentalQuality,
          direction: 'UP' as const,
          volume: 1000,
          momentum: 1,
          timestamp: currentTime
        },
        {
          timeframe: 'sentiment',
          strength: dataQuality.sentimentQuality * 10,
          confidence: dataQuality.sentimentQuality,
          direction: 'UP' as const,
          volume: 1000,
          momentum: 1,
          timestamp: currentTime
        },
        {
          timeframe: 'quantitative',
          strength: dataQuality.quantitativeQuality * 10,
          confidence: dataQuality.quantitativeQuality,
          direction: 'UP' as const,
          volume: 1000,
          momentum: 1,
          timestamp: currentTime
        }
      ];

      const marketCondition = {
        type: 'TRENDING' as const,
        strength: dataQuality.overallQuality,
        volatility: 0.5,
        volume: 1000000, // 默认成交量
        trend: 'bullish' // 默认趋势
      };

      const weightingResult = await this.dynamicWeightingService.allocate(trends, marketCondition);

      optimizedWeights = {
        technical: weightingResult.weights['technical'] || 0.35,
        fundamental: weightingResult.weights['fundamental'] || 0.30,
        sentiment: weightingResult.weights['sentiment'] || 0.20,
        quantitative: weightingResult.weights['quantitative'] || 0.15
      };

      // 使用优化权重计算综合评分
      combinedScore =
        technicalScore * optimizedWeights.technical +
        fundamentalScore * optimizedWeights.fundamental +
        sentimentScore * optimizedWeights.sentiment +
        quantitativeScore * optimizedWeights.quantitative;

      this.logger.info('权重优化结果', {
        originalWeights: { technical: 0.3, fundamental: 0.3, sentiment: 0.2, quantitative: 0.2 },
        optimizedWeights: optimizedWeights,
        dataQuality: dataQuality,
        strategy: weightingResult.strategy,
        confidence: weightingResult.confidence
      });

    } catch (error) {
      this.logger.error('动态权重计算失败，使用默认权重', { error });

      // 使用默认权重
      optimizedWeights = {
        technical: 0.35,
        fundamental: 0.30,
        sentiment: 0.20,
        quantitative: 0.15
      };

      // 使用默认权重计算综合评分
      combinedScore =
        technicalScore * optimizedWeights.technical +
        fundamentalScore * optimizedWeights.fundamental +
        sentimentScore * optimizedWeights.sentiment +
        quantitativeScore * optimizedWeights.quantitative;
    }

    // 信号强度
    let signalStrength: 'WEAK' | 'MODERATE' | 'STRONG';
    if (combinedScore >= 70 || combinedScore <= 30) {
      signalStrength = 'STRONG';
    } else if (combinedScore >= 60 || combinedScore <= 40) {
      signalStrength = 'MODERATE';
    } else {
      signalStrength = 'WEAK';
    }

    // 交易建议
    let recommendation: 'BUY' | 'SELL' | 'HOLD';
    if (combinedScore >= 65) {
      recommendation = 'BUY';
    } else if (combinedScore <= 35) {
      recommendation = 'SELL';
    } else {
      recommendation = 'HOLD';
    }

    // 使用高级置信度计算器
    const marketContext = {
      technical,
      fundamental,
      sentiment,
      quantitative,
      combinedScore,
      signalStrength
    };

    const confidenceResult = this.confidenceCalculator.calculateLegacyConfidence(
      combinedScore,
      dataQuality,
      marketContext
    );

    const confidence = confidenceResult.overall * 100; // 转换为百分比

    // 基于置信度结果确定风险级别
    let riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
    if (confidenceResult.reliability === 'HIGH') {
      riskLevel = 'LOW';
    } else if (confidenceResult.reliability === 'MEDIUM') {
      riskLevel = 'MEDIUM';
    } else {
      riskLevel = 'HIGH';
    }

    return {
      technicalScore: technicalScore,
      fundamentalScore: fundamentalScore,
      sentimentScore: sentimentScore,
      quantitativeScore: quantitativeScore,
      combinedScore: combinedScore,
      signalStrength: signalStrength,
      recommendation,
      confidence,
      riskLevel: riskLevel
    };
  }

  /**
   * 计算技术面评分
   */
  private calculateTechnicalScore(technical: IntegratedMarketData['technical']): number {
    let score = 50; // 基础分

    // RSI评分 (30分)
    if (technical.rsi < 30) {
      score += 15; // 超卖，看涨
    } else if (technical.rsi > 70) {
      score -= 15; // 超买，看跌
    } else if (technical.rsi >= 45 && technical.rsi <= 55) {
      score += 5; // 中性区域
    }

    // MACD评分 (30分)
    if (technical.macd.histogram > 0) {
      score += 15; // 金叉，看涨
    } else {
      score -= 15; // 死叉，看跌
    }

    // 布林带评分 (20分)
    const bollingerPosition = (technical.price - technical.bollinger.lower) /
      (technical.bollinger.upper - technical.bollinger.lower);
    if (bollingerPosition < 0.2) {
      score += 10; // 接近下轨，看涨
    } else if (bollingerPosition > 0.8) {
      score -= 10; // 接近上轨，看跌
    }

    // ADX评分 (20分)
    if (technical.adx > 25) {
      score += 10; // 趋势强劲
    } else if (technical.adx < 20) {
      score -= 5; // 趋势疲弱
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算基本面评分
   */
  private calculateFundamentalScore(fundamental: IntegratedMarketData['fundamental']): number {
    let score = 50; // 基础分

    // 链上数据评分 (40分)
    if (fundamental.onchain.mvrvRatio < 1) {
      score += 20; // 低估
    } else if (fundamental.onchain.mvrvRatio > 3) {
      score -= 20; // 高估
    }

    // 交易所流动评分 (30分)
    const flowRatio = fundamental.onchain.exchangeOutflow > 0 ?
      fundamental.onchain.exchangeInflow / fundamental.onchain.exchangeOutflow : 1;
    if (flowRatio < 0.8) {
      score += 15; // 净流出，看涨
    } else if (flowRatio > 1.2) {
      score -= 15; // 净流入，看跌
    }

    // 网络健康度评分 (30分)
    if (fundamental.network.congestionLevel === 'LOW') {
      score += 10; // 网络健康
    } else if (fundamental.network.congestionLevel === 'HIGH') {
      score -= 10; // 网络拥堵
    }

    // 专业评分 (使用综合分析评分)
    if (fundamental.professional.fundamentalScore > 0) {
      score = (score + fundamental.professional.fundamentalScore) / 2;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算情绪面评分
   */
  private calculateSentimentScore(sentiment: IntegratedMarketData['sentiment']): number {
    let score = 50; // 基础分

    // Fear & Greed指数评分 (60分)
    const fgIndex = sentiment.marketSentiment.fearGreedIndex;
    if (fgIndex <= 20) {
      score += 30; // 极度恐慌，看涨机会
    } else if (fgIndex <= 40) {
      score += 15; // 恐慌，看涨
    } else if (fgIndex >= 80) {
      score -= 30; // 极度贪婪，看跌
    } else if (fgIndex >= 60) {
      score -= 15; // 贪婪，看跌
    }

    // 专业情绪评分 (40分)
    if (sentiment.professionalSentiment.sentimentScore > 0) {
      const professionalContribution = (sentiment.professionalSentiment.sentimentScore - 50) * 0.4;
      score += professionalContribution;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算量化面评分
   */
  private calculateQuantitativeScore(quantitative: IntegratedMarketData['quantitative']): number {
    let score = 50; // 基础分

    // 资金费率评分 (50分)
    const fundingRate = quantitative.derivatives.fundingRate;
    if (fundingRate < -0.0001) {
      score += 25; // 负费率，看涨
    } else if (fundingRate > 0.0001) {
      score -= 25; // 正费率，看跌
    }

    // 多空比评分 (50分)
    const longShortRatio = quantitative.derivatives.longShortRatio;
    if (longShortRatio > 1.5) {
      score -= 25; // 多头过多，看跌
    } else if (longShortRatio < 0.8) {
      score += 25; // 空头过多，看涨
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * 计算数据质量
   */
  private calculateDataQuality(
    technical: IntegratedMarketData['technical'],
    fundamental: IntegratedMarketData['fundamental'],
    sentiment: IntegratedMarketData['sentiment'],
    quantitative: IntegratedMarketData['quantitative']
  ): IntegratedMarketData['dataQuality'] {
    const technicalQuality = technical.reliability;
    const fundamentalQuality = (
      fundamental.onchain.reliability +
      fundamental.professional.reliability +
      fundamental.network.reliability
    ) / 3;
    const sentimentQuality = (
      sentiment.marketSentiment.reliability +
      sentiment.professionalSentiment.reliability
    ) / 2;
    const quantitativeQuality = quantitative.derivatives.reliability;

    const overallQuality = (
      technicalQuality + fundamentalQuality + sentimentQuality + quantitativeQuality
    ) / 4;

    const missingSources: string[] = [];
    if (technicalQuality < 0.5) missingSources.push('TECHNICAL');
    if (fundamentalQuality < 0.5) missingSources.push('FUNDAMENTAL');
    if (sentimentQuality < 0.5) missingSources.push('SENTIMENT');
    if (quantitativeQuality < 0.5) missingSources.push('QUANTITATIVE');

    return {
      technicalQuality: technicalQuality,
      fundamentalQuality: fundamentalQuality,
      sentimentQuality: sentimentQuality,
      quantitativeQuality: quantitativeQuality,
      overallQuality: overallQuality,
      missingSources: missingSources,
      dataFreshness: 0.1 // 数据新鲜度，小时
    };
  }

  // 注意：重复的RSI计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行RSI计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突

  // 注意：重复的布林带计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行布林带计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突

  // calculateADX方法已移除 - 现在使用统一技术指标计算器

  // 注意：重复的MACD计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行MACD计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突

  // 注意：重复的EMA计算方法已被移除
  // 现在统一使用 UnifiedTechnicalIndicatorCalculator 进行EMA计算
  // 这解决了CRIT-002问题：多重技术指标实现冲突



  // calculateADXFromHistoricalData方法已移除 - 现在使用统一技术指标计算器
  // calculateADXFromHistoricalData方法已移除 - 现在使用统一技术指标计算器

  /**
   * Wilder's平滑算法
   * 用于ADX计算中的平滑处理
   */
  private calculateWildersSmoothing(values: number[], period: number): number[] {
    if (values.length < period) return [];

    const smoothed: number[] = [];

    // 第一个值是简单移动平均
    let sum = 0;
    for (let i = 0; i < period; i++) {
      sum += values[i];
    }
    smoothed.push(sum / period);

    // 后续值使用Wilder's平滑公式: (前值 * (period-1) + 当前值) / period
    for (let i = period; i < values.length; i++) {
      const smoothedValue = (smoothed[smoothed.length - 1] * (period - 1) + values[i]) / period;
      smoothed.push(smoothedValue);
    }

    return smoothed;
  }



  /**
   * 健康检查所有数据源
   */
  async healthCheckAllSources(): Promise<{
    mempool: boolean;
    fearGreed: boolean;
    binanceFutures: boolean;
    coinMetrics: boolean;
    sentiCrypt: boolean;
    overall: boolean;
  }> {
    const [mempool, fearGreed, binanceFutures, coinMetrics, sentiCrypt] = await Promise.allSettled([
      this.mempoolAdapter.healthCheck(),
      this.fearGreedAdapter.healthCheck(),
      this.binanceFuturesAdapter.healthCheck(),
      this.coinMetricsAdapter.healthCheck(),
      this.sentiCryptAdapter.healthCheck()
    ]);

    const results = {
      mempool: mempool.status === 'fulfilled' ? (typeof mempool.value === 'boolean' ? mempool.value : (mempool.value as any)?.isHealthy || false) : false,
      fearGreed: fearGreed.status === 'fulfilled' ? (typeof fearGreed.value === 'boolean' ? fearGreed.value : (fearGreed.value as any)?.isHealthy || false) : false,
      binanceFutures: binanceFutures.status === 'fulfilled' ? (typeof binanceFutures.value === 'boolean' ? binanceFutures.value : (binanceFutures.value as any)?.isHealthy || false) : false,
      coinMetrics: coinMetrics.status === 'fulfilled' ? (typeof coinMetrics.value === 'boolean' ? coinMetrics.value : (coinMetrics.value as any)?.isHealthy || false) : false,
      sentiCrypt: sentiCrypt.status === 'fulfilled' ? (typeof sentiCrypt.value === 'boolean' ? sentiCrypt.value : (sentiCrypt.value as any)?.isHealthy || false) : false,
      overall: false
    };

    results.overall = Object.values(results).filter(v => v === true).length >= 4;

    return results;
  }


}

export default RealDataIntegrationService;
