/**
 * 信号降级管理服务
 * 负责在信号生成服务不可用时提供智能降级策略
 */

import { injectable, inject } from 'inversify';
import { IBasicLogger } from '../../../../shared/infrastructure/logging/interfaces/basic-logger.interface';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';

export interface DegradationLevel {
  level: 'NONE' | 'PARTIAL' | 'SEVERE' | 'COMPLETE';
  description: string;
  canUseHistoricalData: boolean;
  canUsePartialFunctionality: boolean;
  recommendedAction: string;
}

export interface DegradedSignalOptions {
  symbol: string;
  useHistoricalData?: boolean;
  preservePartialFunctionality?: boolean;
  explicitWarning?: boolean;
  timeframe?: string;
}

export interface DegradedSignalResponse {
  symbol: string;
  signalType: 'NO_SIGNAL'; // 改为明确的NO_SIGNAL，不再使用UNKNOWN或HOLD
  strength: number;
  confidence: number;
  timestamp: Date;
  expiresAt: Date;
  dataQuality: {
    overall: number;
    sources: string[];
    freshness: number;
    missingDataSources: string[];
  };
  systemStatus: {
    degradationLevel: DegradationLevel;
    affectedComponents: string[];
    estimatedRecoveryTime?: Date;
    lastSuccessfulSignal?: Date;
  };
  message: string;
  warningLevel: 'INFO' | 'WARNING' | 'CRITICAL';
  alternativeDataSources?: string[];
}

@injectable()
export class DegradationManagerService {
  constructor(
    @inject(TYPES.Logger) private readonly logger: IBasicLogger,
  ) {
    this.logger.info('降级管理服务初始化');
  }

  /**
   * 评估当前系统降级级别
   */
  public assessDegradationLevel(error: any): DegradationLevel {
    // 根据错误类型和消息确定降级级别
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    if (errorMessage.includes('网络连接') || errorMessage.includes('timeout') || errorMessage.includes('超时')) {
      return {
        level: 'PARTIAL',
        description: '网络连接问题导致部分服务不可用',
        canUseHistoricalData: true,
        canUsePartialFunctionality: true,
        recommendedAction: '使用历史数据和部分功能继续提供服务'
      };
    } else if (errorMessage.includes('数据源') || errorMessage.includes('data source')) {
      return {
        level: 'PARTIAL',
        description: '部分数据源不可用',
        canUseHistoricalData: true,
        canUsePartialFunctionality: true,
        recommendedAction: '使用可用数据源和历史数据继续提供服务'
      };
    } else if (errorMessage.includes('初始化') || errorMessage.includes('initialization')) {
      return {
        level: 'SEVERE',
        description: '系统初始化失败',
        canUseHistoricalData: false,
        canUsePartialFunctionality: false,
        recommendedAction: '返回明确的系统不可用信号'
      };
    } else {
      return {
        level: 'COMPLETE',
        description: '系统完全不可用',
        canUseHistoricalData: false,
        canUsePartialFunctionality: false,
        recommendedAction: '返回明确的系统不可用信号'
      };
    }
  }

  /**
   * 生成降级信号响应
   */
  public generateDegradedSignal(options: DegradedSignalOptions, error: any): DegradedSignalResponse {
    const degradationLevel = this.assessDegradationLevel(error);
    const timestamp = new Date();
    const expiresAt = new Date(timestamp.getTime() + 5 * 60 * 1000); // 5分钟后过期，比正常信号短

    this.logger.warn('生成降级信号 - 明确返回NO_SIGNAL', {
      symbol: options.symbol,
      degradationLevel: degradationLevel.level,
      error: error instanceof Error ? error.message : String(error)
    });

    // 构建降级信号响应 - 明确返回NO_SIGNAL
    return {
      symbol: options.symbol,
      signalType: 'NO_SIGNAL', // 明确表示不提供信号，而不是提供可能误导的信号
      strength: 0,
      confidence: 0,
      timestamp,
      expiresAt,
      dataQuality: {
        overall: 0,
        sources: ['degraded'],
        freshness: 0,
        missingDataSources: ['所有数据源']
      },
      systemStatus: {
        degradationLevel,
        affectedComponents: ['信号生成服务', '数据集成服务'],
        estimatedRecoveryTime: new Date(timestamp.getTime() + 30 * 60 * 1000) // 预计30分钟后恢复
      },
      message: `信号生成服务暂时不可用 (${degradationLevel.description})。系统无法提供任何交易信号，请等待系统恢复或使用其他数据源。`,
      warningLevel: 'CRITICAL',
      alternativeDataSources: ['请直接查看交易所行情']
    };
  }

  /**
   * 尝试从历史数据生成降级信号
   * 根据新的策略，我们不再提供任何可能误导的信号，即使是基于历史数据的
   */
  public async tryGenerateHistoricalBasedSignal(options: DegradedSignalOptions): Promise<DegradedSignalResponse | null> {
    // 根据新策略，不再使用历史数据生成可能误导的信号
    this.logger.info('根据新策略，不再使用历史数据生成信号', { symbol: options.symbol });
    return null;
    
    // 以下代码保留但不再执行，以便将来如果策略变更可以快速恢复
    /*
    if (!options.useHistoricalData) {
      return null;
    }

    try {
      this.logger.info('尝试使用历史数据生成降级信号', { symbol: options.symbol });
      
      // 这里可以实现从缓存或数据库获取最近的有效信号
      // 目前返回null，表示无法从历史数据生成信号
      return null;
    } catch (error) {
      this.logger.error('从历史数据生成降级信号失败', {
        symbol: options.symbol,
        error: error instanceof Error ? error.message : String(error)
      });
      return null;
    }
    */
  }

  /**
   * 检查系统是否可以恢复
   * 可以实现为定期检查系统状态，并在恢复后通知
   */
  public async checkSystemRecovery(): Promise<boolean> {
    // 实现系统恢复检查逻辑
    return false;
  }
}