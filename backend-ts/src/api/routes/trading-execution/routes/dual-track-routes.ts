/**
 * 交易执行双轨制管理路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TradingExecutionApplicationService } from '../../../../contexts/trading-execution/application/services/trading-execution-application-service';
import { Logger } from 'winston';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
    }
  }
}

/**
 * 创建双轨制管理路由
 * 包含：/dual-track/* 相关端点
 */
export function createDualTrackRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取交易执行服务
  const getTradingExecutionService = (): TradingExecutionApplicationService => {
    return container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );
  };

  // 异步处理器
  const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => next(error));
  };

  // 双轨制状态查询
  router.get('/dual-track/status', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取双轨制状态', {
        requestId: req.id,
      });

      // 实现基本的双轨制状态查询
      const dualTrackStatus = {
        enabled: true,
        mode: 'simulation', // simulation | live | hybrid
        simulationAccount: {
          id: 'sim_account_001',
          status: 'active',
          balance: 10000,
          positions: 0,
          lastSync: new Date().toISOString()
        },
        liveAccount: {
          id: 'live_account_001',
          status: 'connected',
          balance: 0,
          positions: 0,
          lastSync: new Date().toISOString()
        },
        syncStatus: {
          isInSync: true,
          lastSyncTime: new Date().toISOString(),
          syncErrors: 0
        },
        performance: {
          simulationPnl: 0,
          livePnl: 0,
          correlation: 0.95
        }
      };

      return res.status(200).json({
        success: true,
        data: dualTrackStatus,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制状态失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制状态失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 双轨制账户查询
  router.get('/dual-track/accounts', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取双轨制账户列表', {
        requestId: req.id,
      });

      // 实现基本的双轨制账户查询
      const accounts = [
        {
          id: 'sim_account_001',
          type: 'simulation',
          name: '模拟账户',
          status: 'active',
          balance: {
            total: 10000,
            available: 9500,
            frozen: 500
          },
          positions: [],
          createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          lastActivity: new Date().toISOString()
        },
        {
          id: 'live_account_001',
          type: 'live',
          name: '实盘账户',
          status: 'connected',
          balance: {
            total: 0,
            available: 0,
            frozen: 0
          },
          positions: [],
          createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          lastActivity: new Date().toISOString()
        }
      ];

      return res.status(200).json({
        success: true,
        data: accounts,
        total: accounts.length,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制账户失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制账户失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 账户配对信息
  router.get('/dual-track/account-pairing', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取账户配对信息', {
        requestId: req.id,
      });

      // 实现基本的账户配对信息查询
      const accountPairing = {
        pairId: 'pair_001',
        simulationAccount: {
          id: 'sim_account_001',
          name: '模拟账户',
          status: 'active'
        },
        liveAccount: {
          id: 'live_account_001',
          name: '实盘账户',
          status: 'connected'
        },
        pairingStatus: 'active',
        syncSettings: {
          autoSync: true,
          syncInterval: 5000, // 5秒
          syncOnSignal: true,
          syncOnPositionChange: true
        },
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        lastSync: new Date().toISOString()
      };

      return res.status(200).json({
        success: true,
        data: accountPairing,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取账户配对信息失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取账户配对信息失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 同步状态查询
  router.get('/dual-track/sync-status', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取双轨制同步状态', {
        requestId: req.id,
      });

      // 实现基本的同步状态查询
      const syncStatus = {
        isEnabled: true,
        isActive: true,
        lastSyncTime: new Date().toISOString(),
        nextSyncTime: new Date(Date.now() + 5000).toISOString(),
        syncInterval: 5000,
        syncStats: {
          totalSyncs: 1440, // 一天的同步次数
          successfulSyncs: 1435,
          failedSyncs: 5,
          successRate: 99.65
        },
        recentSyncHistory: [
          {
            timestamp: new Date().toISOString(),
            status: 'success',
            duration: 150,
            itemsSynced: 3
          },
          {
            timestamp: new Date(Date.now() - 5000).toISOString(),
            status: 'success',
            duration: 120,
            itemsSynced: 2
          }
        ],
        errors: []
      };

      return res.status(200).json({
        success: true,
        data: syncStatus,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制同步状态失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制同步状态失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 性能对比
  router.get('/dual-track/performance-comparison', asyncHandler(async (req: Request, res: Response) => {
    try {
      const tradingExecutionService = getTradingExecutionService();
      const comparison = await tradingExecutionService.getDualTrackPerformanceComparison();

      logger.info('获取双轨制性能对比完成', {
        requestId: req.id
      });

      return res.json({
        success: true,
        data: comparison,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制性能对比失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制性能对比失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 双轨制指标
  router.get('/dual-track/metrics', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取双轨制指标', {
        requestId: req.id,
      });

      // 实现基本的双轨制指标查询
      const metrics = {
        performance: {
          simulationAccount: {
            totalPnl: 150.50,
            dailyPnl: 25.30,
            winRate: 0.65,
            totalTrades: 45,
            avgTradeSize: 100
          },
          liveAccount: {
            totalPnl: 0,
            dailyPnl: 0,
            winRate: 0,
            totalTrades: 0,
            avgTradeSize: 0
          },
          correlation: 0.95
        },
        risk: {
          maxDrawdown: 0.05,
          sharpeRatio: 1.2,
          volatility: 0.15,
          var95: 0.03
        },
        execution: {
          avgLatency: 150,
          successRate: 0.99,
          slippage: 0.001
        },
        sync: {
          syncAccuracy: 0.998,
          avgSyncTime: 120,
          syncErrors: 2
        }
      };

      return res.status(200).json({
        success: true,
        data: metrics,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制指标失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制指标失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  // 双轨制统计
  router.get('/dual-track/statistics', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取双轨制统计', {
        requestId: req.id,
      });

      // 实现基本的双轨制统计查询
      const statistics = {
        overview: {
          totalDays: 30,
          activeDays: 28,
          totalTrades: 156,
          profitableDays: 22,
          profitability: 0.73
        },
        accounts: {
          simulation: {
            totalTrades: 156,
            winningTrades: 102,
            losingTrades: 54,
            winRate: 0.65,
            avgWin: 15.50,
            avgLoss: -8.20,
            profitFactor: 1.93
          },
          live: {
            totalTrades: 0,
            winningTrades: 0,
            losingTrades: 0,
            winRate: 0,
            avgWin: 0,
            avgLoss: 0,
            profitFactor: 0
          }
        },
        timeframes: {
          daily: {
            avgTrades: 5.2,
            avgPnl: 5.02,
            bestDay: 45.30,
            worstDay: -12.50
          },
          weekly: {
            avgTrades: 36.4,
            avgPnl: 35.14,
            bestWeek: 125.80,
            worstWeek: -25.60
          }
        }
      };

      return res.status(200).json({
        success: true,
        data: statistics,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取双轨制统计失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取双轨制统计失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
