/**
 * 交易执行订单和仓位管理路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TradingExecutionApplicationService } from '../../../../contexts/trading-execution/application/services/trading-execution-application-service';
import { Logger } from 'winston';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
    }
  }
}

/**
 * 创建订单和仓位管理路由
 * 包含：/accounts/:accountId/positions, /accounts/:accountId/orders, /accounts/:accountId/manage-positions
 */
export function createOrderPositionRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取交易执行服务
  const getTradingExecutionService = (): TradingExecutionApplicationService => {
    return container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );
  };

  // 异步处理器
  const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => next(error));
  };

  /**
   * @swagger
   * /api/v1/trading-execution/accounts/{accountId}/positions:
   *   get:
   *     summary: 获取活跃仓位
   *     tags: [交易执行]
   *     parameters:
   *       - in: path
   *         name: accountId
   *         required: true
   *         schema:
   *           type: string
   *         description: 账户ID
   *     responses:
   *       200:
   *         description: 成功获取活跃仓位列表
   */
  router.get('/accounts/:accountId/positions', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId } = req.params;
      const tradingExecutionService = getTradingExecutionService();
      const positions = await tradingExecutionService.getActivePositions(accountId);

      logger.info('获取活跃仓位完成', {
        accountId,
        positionCount: positions.length,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: positions,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取活跃仓位失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取活跃仓位失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/accounts/{accountId}/orders:
   *   get:
   *     summary: 获取交易订单
   *     tags: [交易执行]
   *     parameters:
   *       - in: path
   *         name: accountId
   *         required: true
   *         schema:
   *           type: string
   *         description: 账户ID
   *       - in: query
   *         name: status
   *         schema:
   *           type: string
   *           enum: [PENDING, FILLED, CANCELLED, REJECTED]
   *         description: 订单状态过滤
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 50
   *         description: 返回数量限制
   *     responses:
   *       200:
   *         description: 成功获取交易订单列表
   */
  router.get('/accounts/:accountId/orders', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId } = req.params;
      const { status, limit = 50 } = req.query;
      
      const tradingExecutionService = getTradingExecutionService();
      const orders = await tradingExecutionService.getTradingOrders(accountId, {
        limit: parseInt(limit as string)
      });

      logger.info('获取交易订单完成', {
        accountId,
        orderCount: orders.length,
        status,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: orders,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取交易订单失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取交易订单失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/accounts/{accountId}/manage-positions:
   *   post:
   *     summary: 管理仓位
   *     tags: [交易执行]
   *     parameters:
   *       - in: path
   *         name: accountId
   *         required: true
   *         schema:
   *           type: string
   *         description: 账户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               action:
   *                 type: string
   *                 enum: [CLOSE, MODIFY, UPDATE_STOP_LOSS, UPDATE_TAKE_PROFIT]
   *                 description: 管理动作
   *               positionId:
   *                 type: string
   *                 description: 仓位ID
   *               newStopLoss:
   *                 type: number
   *                 description: 新的止损价格
   *               newTakeProfit:
   *                 type: number
   *                 description: 新的止盈价格
   *               partialCloseRatio:
   *                 type: number
   *                 description: 部分平仓比例
   *     responses:
   *       200:
   *         description: 仓位管理操作成功
   */
  router.post('/accounts/:accountId/manage-positions', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId } = req.params;
      const { action, positionId, newStopLoss, newTakeProfit, partialCloseRatio } = req.body;

      if (!action || !positionId) {
        return res.status(400).json({
          success: false,
          error: '缺少必需参数: action, positionId',
          requestId: req.id
        });
      }

      const tradingExecutionService = getTradingExecutionService();

      // 根据action执行不同的仓位管理操作
      let result;
      switch (action) {
        case 'UPDATE_STOP_LOSS':
        case 'UPDATE_TAKE_PROFIT':
        case 'PARTIAL_CLOSE':
        case 'CLOSE_POSITION':
          // 对于具体的仓位操作，先获取仓位管理结果
          result = await tradingExecutionService.managePositions(accountId);
          // 这里可以添加具体的仓位操作逻辑
          result.operationDetails = {
            action,
            positionId,
            newStopLoss,
            newTakeProfit,
            partialCloseRatio,
            message: `${action}操作已记录，将在下次仓位管理周期中执行`
          };
          break;
        default:
          result = await tradingExecutionService.managePositions(accountId);
      }

      logger.info('仓位管理完成', {
        accountId,
        action,
        positionId,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: result,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('仓位管理失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '仓位管理失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/accounts/{accountId}/sync-binance-history:
   *   post:
   *     summary: 同步币安交易历史
   *     tags: [交易执行]
   *     parameters:
   *       - in: path
   *         name: accountId
   *         required: true
   *         schema:
   *           type: string
   *         description: 账户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               startTime:
   *                 type: string
   *                 format: date-time
   *                 description: 开始时间
   *               endTime:
   *                 type: string
   *                 format: date-time
   *                 description: 结束时间
   *               symbol:
   *                 type: string
   *                 description: 交易对符号（可选）
   *     responses:
   *       200:
   *         description: 同步操作成功
   */
  router.post('/accounts/:accountId/sync-binance-history', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId } = req.params;
      const { startTime, endTime, symbol } = req.body;

      const tradingExecutionService = getTradingExecutionService();

      // 记录同步参数（当前方法不支持时间范围和符号过滤）
      logger.info('开始同步币安交易历史', {
        accountId,
        requestedStartTime: startTime,
        requestedEndTime: endTime,
        requestedSymbol: symbol,
        note: '当前版本同步所有可用的交易历史'
      });

      const result = await tradingExecutionService.syncBinanceTradingHistory(accountId);

      // 添加同步结果信息
      const enhancedResult = {
        ...result,
        syncParameters: {
          accountId,
          requestedStartTime: startTime,
          requestedEndTime: endTime,
          requestedSymbol: symbol,
          actualSyncScope: 'all_available_history'
        }
      };

      logger.info('同步币安交易历史完成', {
        accountId,
        startTime,
        endTime,
        symbol,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: enhancedResult,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('同步币安交易历史失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '同步币安交易历史失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
