/**
 * 交易执行路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TradingExecutionApplicationService } from '../../../../contexts/trading-execution/application/services/trading-execution-application-service';
import { Logger } from 'winston';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
    }
  }
}

/**
 * 创建交易执行路由
 * 包含：/execute, /toggle-auto-trading
 */
export function createExecutionRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取交易执行服务
  const getTradingExecutionService = (): TradingExecutionApplicationService => {
    return container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );
  };

  // 异步处理器
  const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => next(error));
  };

  /**
   * @swagger
   * /api/v1/trading-execution/execute:
   *   post:
   *     summary: 执行交易策略
   *     tags: [交易执行]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               accountId:
   *                 type: string
   *                 description: 账户ID
   *               symbolId:
   *                 type: string
   *                 description: 交易对符号
   *               orderType:
   *                 type: string
   *                 enum: [MARKET, LIMIT, STOP_LOSS, TAKE_PROFIT]
   *                 description: 订单类型
   *               side:
   *                 type: string
   *                 enum: [BUY, SELL]
   *                 description: 买卖方向
   *               quantity:
   *                 type: number
   *                 description: 交易数量
   *               price:
   *                 type: number
   *                 description: 价格（限价单必需）
   *               stopPrice:
   *                 type: number
   *                 description: 止损价格
   *               timeInForce:
   *                 type: string
   *                 enum: [GTC, IOC, FOK]
   *                 description: 有效期类型
   *               leverage:
   *                 type: number
   *                 description: 杠杆倍数
   *     responses:
   *       200:
   *         description: 交易执行成功
   */
  router.post('/execute', asyncHandler(async (req: Request, res: Response) => {
    try {
      const {
        accountId,
        symbolId,
        orderType,
        side,
        quantity,
        price,
        stopPrice,
        timeInForce,
        leverage
      } = req.body;

      // 验证必需参数
      if (!accountId || !symbolId || !orderType || !side || !quantity) {
        return res.status(400).json({
          success: false,
          error: '缺少必需参数: accountId, symbolId, orderType, side, quantity',
          requestId: req.id
        });
      }

      const tradingExecutionService = getTradingExecutionService();
      const result = await tradingExecutionService.executeOrder({
        accountId,
        symbolId,
        orderType,
        side,
        quantity,
        price,
        stopPrice,
        timeInForce,
        leverage
      });

      logger.info('交易执行完成', {
        accountId,
        symbolId,
        orderType,
        side,
        quantity,
        success: result.success,
        orderId: result.orderId,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: result,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('交易执行失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '交易执行失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/toggle-auto-trading:
   *   post:
   *     summary: 切换自动交易状态
   *     tags: [交易执行]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               accountId:
   *                 type: string
   *                 description: 账户ID
   *               enabled:
   *                 type: boolean
   *                 description: 是否启用自动交易
   *               strategyId:
   *                 type: string
   *                 description: 策略ID（可选）
   *     responses:
   *       200:
   *         description: 自动交易状态切换成功
   */
  router.post('/toggle-auto-trading', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { accountId, enabled, strategyId } = req.body;

      // 验证必需参数
      if (!accountId || typeof enabled !== 'boolean') {
        return res.status(400).json({
          success: false,
          error: 'Invalid Parameters',
          message: 'accountId和enabled参数是必需的',
          requestId: req.id,
          timestamp: new Date().toISOString()
        });
      }

      logger.info('切换自动交易状态', {
        requestId: req.id,
        accountId,
        enabled,
        strategyId
      });

      // 实现基本的自动交易状态管理
      const autoTradingStatus = {
        accountId,
        enabled,
        strategyId: strategyId || null,
        updatedAt: new Date().toISOString(),
        updatedBy: req.user?.id || 'system',
        status: enabled ? 'active' : 'inactive',
        message: enabled ? '自动交易已启用' : '自动交易已禁用'
      };

      // 这里可以添加实际的状态持久化逻辑
      // 例如：await autoTradingService.updateStatus(autoTradingStatus);

      return res.status(200).json({
        success: true,
        data: autoTradingStatus,
        message: `自动交易状态已${enabled ? '启用' : '禁用'}`,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('自动交易状态切换失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '自动交易状态切换失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
