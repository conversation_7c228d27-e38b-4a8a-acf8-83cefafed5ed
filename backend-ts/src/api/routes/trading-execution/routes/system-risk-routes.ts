/**
 * 交易执行系统状态和风险管理路由模块
 * 从大文件中拆分出来，遵循单一职责原则
 */

import { Router, Request, Response } from 'express';
import { Container } from 'inversify';
import { TYPES } from '../../../../shared/infrastructure/di/types/index';
import { TradingExecutionApplicationService } from '../../../../contexts/trading-execution/application/services/trading-execution-application-service';
import { Logger } from 'winston';

// 扩展Request接口
declare global {
  namespace Express {
    interface Request {
      id?: string;
      startTime?: number;
    }
  }
}

/**
 * 创建系统状态和风险管理路由
 * 包含：/status, /risk-events, /credentials
 */
export function createSystemRiskRoutes(container: Container): Router {
  const router = Router();
  const logger = container.get<Logger>(TYPES.Logger);

  // 获取交易执行服务
  const getTradingExecutionService = (): TradingExecutionApplicationService => {
    return container.get<TradingExecutionApplicationService>(
      TYPES.TradingExecution.TradingExecutionApplicationService
    );
  };

  // 异步处理器
  const asyncHandler = (fn: Function) => (req: Request, res: Response, next: Function) => {
    Promise.resolve(fn(req, res, next)).catch((error) => next(error));
  };

  /**
   * @swagger
   * /api/v1/trading-execution/status:
   *   get:
   *     summary: 获取系统状态
   *     tags: [交易执行]
   *     responses:
   *       200:
   *         description: 成功获取系统状态
   */
  router.get('/status', asyncHandler(async (req: Request, res: Response) => {
    try {
      logger.info('获取交易执行系统状态', {
        requestId: req.id,
      });

      // 获取系统基本状态
      const systemStatus = {
        service: 'trading-execution',
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        version: process.env.npm_package_version || '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        components: {
          database: {
            status: 'healthy',
            responseTime: 0,
            lastChecked: new Date().toISOString()
          },
          riskManagement: {
            status: 'healthy',
            responseTime: 0,
            lastChecked: new Date().toISOString()
          },
          orderExecution: {
            status: 'healthy',
            responseTime: 0,
            lastChecked: new Date().toISOString()
          }
        },
        metrics: {
          memoryUsage: process.memoryUsage(),
          cpuUsage: process.cpuUsage(),
          activeConnections: 0,
          pendingOrders: 0,
          activePositions: 0
        }
      };

      return res.status(200).json({
        success: true,
        data: systemStatus,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取系统状态失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取系统状态失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/risk-events:
   *   get:
   *     summary: 获取风险事件
   *     tags: [交易执行]
   *     parameters:
   *       - in: query
   *         name: severity
   *         schema:
   *           type: string
   *           enum: [LOW, MEDIUM, HIGH, CRITICAL]
   *         description: 风险等级过滤
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 50
   *         description: 返回数量限制
   *     responses:
   *       200:
   *         description: 成功获取风险事件列表
   */
  router.get('/risk-events', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { severity, limit = 50 } = req.query;
      
      const tradingExecutionService = getTradingExecutionService();
      const riskEvents = await tradingExecutionService.getRiskEvents({
        severity: severity as string,
        limit: parseInt(limit as string)
      });

      logger.info('获取风险事件完成', {
        eventCount: riskEvents.length,
        severity,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: riskEvents,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取风险事件失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取风险事件失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  /**
   * @swagger
   * /api/v1/trading-execution/credentials:
   *   post:
   *     summary: 配置交易凭证
   *     tags: [交易执行]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               exchange:
   *                 type: string
   *                 enum: [BINANCE, OKEX, HUOBI]
   *                 description: 交易所名称
   *               apiKey:
   *                 type: string
   *                 description: API密钥
   *               apiSecret:
   *                 type: string
   *                 description: API密钥
   *               passphrase:
   *                 type: string
   *                 description: 密码短语（某些交易所需要）
   *               testnet:
   *                 type: boolean
   *                 description: 是否为测试网
   *     responses:
   *       200:
   *         description: 凭证配置成功
   *   get:
   *     summary: 获取交易凭证配置
   *     tags: [交易执行]
   *     responses:
   *       200:
   *         description: 成功获取凭证配置
   */
  router.post('/credentials', asyncHandler(async (req: Request, res: Response) => {
    try {
      const { exchange, apiKey, apiSecret, passphrase, testnet } = req.body;

      if (!exchange || !apiKey || !apiSecret) {
        return res.status(400).json({
          success: false,
          error: '缺少必需参数: exchange, apiKey, apiSecret',
          requestId: req.id
        });
      }

      const tradingExecutionService = getTradingExecutionService();
      const result = await tradingExecutionService.configureCredentials({
        exchange,
        apiKey,
        secretKey: apiSecret,
        passphrase,
        testnet: testnet || false
      });

      logger.info('配置交易凭证完成', {
        exchange,
        testnet,
        requestId: req.id
      });

      return res.json({
        success: true,
        data: result,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('配置交易凭证失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '配置交易凭证失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  router.get('/credentials', asyncHandler(async (req: Request, res: Response) => {
    try {
      const tradingExecutionService = getTradingExecutionService();
      const credentials = await tradingExecutionService.getCredentials();

      logger.info('获取交易凭证配置完成', {
        requestId: req.id
      });

      return res.json({
        success: true,
        data: credentials,
        requestId: req.id,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      logger.error('获取交易凭证配置失败', {
        error: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });

      return res.status(500).json({
        success: false,
        error: '获取交易凭证配置失败',
        details: error instanceof Error ? error.message : '未知错误',
        requestId: req.id
      });
    }
  }));

  return router;
}
