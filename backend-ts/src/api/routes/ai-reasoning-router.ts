import { Request, Response } from 'express';
import { Container } from 'inversify';
import { BaseRouter } from './base-router';
import { TYPES } from '../../shared/infrastructure/di/types/index';
import { AIReasoningApplicationService } from '../../contexts/ai-reasoning/application/services/ai-reasoning-application-service';

/**
 * AI推理路由
 */
export class AIReasoningRouter extends BaseRouter {
  private readonly decisionCache: Map<string, { decision: any; timestamp: number }> = new Map();
  private readonly CACHE_DURATION = 3 * 60 * 1000; // 3分钟缓存

  constructor(container: Container) {
    super(container);
  }

  // 延迟获取AI推理服务
  private getAIReasoningService(): AIReasoningApplicationService {
    return this.container.get<AIReasoningApplicationService>(TYPES.AIReasoning.AIReasoningApplicationService);
  }

  protected setupRoutes(): void {
    /**
     * @swagger
     * /api/v1/ai/decision/{symbol}:
     *   get:
     *     summary: 获取AI投资决策
     *     tags: [AI推理]
     *     parameters:
     *       - in: path
     *         name: symbol
     *         required: true
     *         schema:
     *           type: string
     *         description: 交易对符号 (如 BTCUSDT)
     *       - in: query
     *         name: riskTolerance
     *         schema:
     *           type: string
     *           enum: [conservative, moderate, aggressive]
     *         description: 风险承受能力
     *       - in: query
     *         name: analysisDepth
     *         schema:
     *           type: string
     *           enum: [basic, standard, deep]
     *         description: 分析深度
     *     responses:
     *       200:
     *         description: 成功获取AI投资决策
     *         content:
     *           application/json:
     *             schema:
     *               type: object
     *               properties:
     *                 success:
     *                   type: boolean
     *                 data:
     *                   type: object
     *                   properties:
     *                     action:
     *                       type: string
     *                       enum: [BUY, SELL, HOLD]
     *                     confidence:
     *                       type: number
     *                       minimum: 0
     *                       maximum: 1
     *                     strength:
     *                       type: integer
     *                       minimum: 1
     *                       maximum: 10
     *                     reasoning:
     *                       type: array
     *                       items:
     *                         type: string
     *                     targetPrice:
     *                       type: number
     *                       nullable: true
     *                     stopLoss:
     *                       type: number
     *                       nullable: true
     *                     position:
     *                       type: number
     *                     timeframe:
     *                       type: string
     *                     riskLevel:
     *                       type: string
     *                     expectedReturn:
     *                       type: number
     *                     monitoringPoints:
     *                       type: array
     *                       items:
     *                         type: object
     *                     adjustmentTriggers:
     *                       type: array
     *                       items:
     *                         type: object
     *                 timestamp:
     *                   type: string
     *                   format: date-time
     */
    this.router.get('/decision/:symbol', this.asyncHandler(this.getAIDecision.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/analyze:
     *   post:
     *     summary: 执行AI市场分析
     *     tags: [AI推理]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               symbol:
     *                 type: string
     *                 description: 交易对符号
     *               timeframe:
     *                 type: string
     *                 description: 时间框架
     *               analysisType:
     *                 type: string
     *                 description: 分析类型
     *     responses:
     *       200:
     *         description: AI分析结果
     */
    this.router.post('/analyze', this.asyncHandler(this.executeAnalysis.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/reasoning:
     *   post:
     *     summary: 执行AI推理分析
     *     tags: [AI推理]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               query:
     *                 type: string
     *                 description: 推理查询
     *               context:
     *                 type: object
     *                 description: 上下文信息
     *               userProfile:
     *                 type: object
     *                 description: 用户画像
     *     responses:
     *       200:
     *         description: 推理分析结果
     */
    this.router.post('/reasoning', this.asyncHandler(this.executeReasoning.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/market-context:
     *   get:
     *     summary: 获取市场上下文分析
     *     tags: [AI推理]
     *     parameters:
     *       - in: query
     *         name: symbol
     *         required: true
     *         schema:
     *           type: string
     *         description: 交易对符号
     *     responses:
     *       200:
     *         description: 市场上下文分析结果
     */
    this.router.get('/market-context', this.asyncHandler(this.getMarketContext.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/predictions/short-term:
     *   get:
     *     summary: 获取短期预测
     *     tags: [AI推理]
     *     responses:
     *       200:
     *         description: 短期预测结果
     */
    this.router.get('/predictions/short-term', this.asyncHandler(this.getShortTermPredictions.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/predictions/long-term:
     *   get:
     *     summary: 获取长期预测
     *     tags: [AI推理]
     *     responses:
     *       200:
     *         description: 长期预测结果
     */
    this.router.get('/predictions/long-term', this.asyncHandler(this.getLongTermPredictions.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/predictions/stats:
     *   get:
     *     summary: 获取预测统计
     *     tags: [AI推理]
     *     responses:
     *       200:
     *         description: 预测统计结果
     */
    this.router.get('/predictions/stats', this.asyncHandler(this.getPredictionStats.bind(this)));

    /**
     * @swagger
     * /api/v1/ai/health:
     *   get:
     *     summary: AI推理服务健康检查
     *     tags: [AI推理]
     *     responses:
     *       200:
     *         description: 健康检查结果
     */
    this.router.get('/health', this.asyncHandler(this.getHealthStatus.bind(this)));
  }

  /**
   * 获取AI投资决策
   */
  private async getAIDecision(req: Request, res: Response): Promise<void> {
    try {
      const { symbol } = req.params;
      const { riskTolerance = 'moderate', analysisDepth = 'standard' } = req.query;

      if (!symbol) {
        res.error('Symbol parameter is required', 400);
        return;
      }

      // 检查缓存
      const cacheKey = `${symbol}-${riskTolerance}-${analysisDepth}`;
      const cached = this.decisionCache.get(cacheKey);
      const now = Date.now();

      if (cached && (now - cached.timestamp) < this.CACHE_DURATION) {
        this.logger.info('使用缓存的AI投资决策', {
          requestId: req.id,
          symbol,
          cacheAge: `${Math.round((now - cached.timestamp) / 1000)  }s`
        });
        
        res.success(cached.decision);
        return;
      }

      this.logger.info('生成新的AI投资决策', {
        requestId: req.id,
        symbol,
        riskTolerance,
        analysisDepth,
      });

      const aiReasoningService = this.getAIReasoningService();
      const decision = await aiReasoningService.executeReasoning({
        query: `基于以下用户画像和市场数据，为 ${symbol} 提供投资决策：风险承受能力 ${riskTolerance}, 分析深度 ${analysisDepth}`,
        context: {
          symbol,
          userProfile: {
            riskTolerance: riskTolerance as 'conservative' | 'moderate' | 'aggressive',
          },
          marketData: {}
        },
        analysisDepth: (analysisDepth as string) as "quick" | "standard" | "comprehensive",
      });

      // 转换为前端期望的格式
      const response = {
        action: decision.action,
        confidence: decision.confidence,
        strength: Math.round(decision.confidence * 10),
        reasoning: Array.isArray(decision.reasoning) ? decision.reasoning : [],
        targetPrice: decision.executionPlan?.targetPrice,
        stopLoss: decision.riskControls?.stopLoss,
        position: decision.executionPlan?.positionSize || 10,
        timeframe: decision.executionPlan?.timeframe || 'N/A',
        riskLevel: decision.riskControls?.riskLevel || 'medium',
        expectedReturn: decision.expectedReturn?.target || 0,
        monitoringPoints: Array.isArray(decision.monitoringPoints) ? decision.monitoringPoints : [],
        adjustmentTriggers: Array.isArray(decision.adjustmentTriggers) ? decision.adjustmentTriggers : [],
      };

      // 保存到缓存
      this.decisionCache.set(cacheKey, {
        decision: response,
        timestamp: now
      });

      this.logger.info('AI投资决策制定完成', {
        requestId: req.id,
        symbol,
        action: decision.action,
        confidence: decision.confidence,
        cached: true
      });

      // 使用统一的APIResponse格式
      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 执行AI市场分析
   */
  private async executeAnalysis(req: Request, res: Response): Promise<void> {
    try {
      const { symbol, timeframe, analysisType = 'comprehensive' } = req.body;

      if (!symbol) {
        res.error('Symbol is required', 400);
        return;
      }

      this.logger.info('执行AI市场分析', {
        requestId: req.id,
        symbol,
        timeframe,
        analysisType,
      });

      // 构建分析查询
      const query = `分析${symbol}在${timeframe || '1h'}时间框架下的市场状况，提供${analysisType}分析`;

      const aiReasoningService = this.getAIReasoningService();
      const result = await aiReasoningService.executeReasoning({
        query,
        context: {
          symbol,
          timeframe: timeframe || '1h',
          analysisType,
          requestType: 'marketAnalysis'
        },
        analysisDepth: 'standard',
      });

      // 格式化响应以匹配前端期望
      const response = {
        symbol,
        timeframe: timeframe || '1h',
        analysisType,
        analysis: result.analysis || result.reasoning || '分析完成',
        confidence: result.confidence || 0.8,
        recommendations: result.recommendations || [],
        riskLevel: result.riskLevel || 'medium',
        timestamp: new Date().toISOString(),
      };

      res.success(response);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 执行AI推理分析
   */
  private async executeReasoning(req: Request, res: Response): Promise<void> {
    try {
      const { query, context, userProfile, marketData, position, analysisDepth } = req.body;

      if (!query) {
        res.error('Query is required', 400);
        return;
      }

      this.logger.info('执行AI推理分析', {
        requestId: req.id,
        query: query.substring(0, 100),
      });

      const aiReasoningService = this.getAIReasoningService();
      const result = await aiReasoningService.executeReasoning({
        query,
        context: context || {},
        analysisDepth: analysisDepth || 'standard',
      });

      res.success(result);
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取市场上下文分析
   */
  private async getMarketContext(req: Request, res: Response): Promise<void> {
    try {
      const { symbol } = req.query;

      if (!symbol) {
        res.error('Symbol parameter is required', 400);
        return;
      }

      this.logger.info('获取市场上下文分析', {
        requestId: req.id,
        symbol,
      });

      // 🔥 修复虚假实现：使用真实的AI推理服务获取市场上下文
      // const aiReasoningService = this.getAIReasoningService();
      // const marketContext = await aiReasoningService.getMarketContext(symbol as string);
      
      // if (!marketContext) {
      //   throw new Error(`无法获取 ${symbol} 的市场上下文分析`);
      // }

      res.success({ message: '此功能正在开发中，暂不可用。' });
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取短期预测
   */
  private async getShortTermPredictions(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('获取短期预测', {
        requestId: req.id,
      });

      // 🔥 修复虚假实现：使用真实的AI推理服务获取短期预测
      // const aiReasoningService = this.getAIReasoningService();
      // const shortTermPredictions = await aiReasoningService.getShortTermPredictions();
      
      // if (!shortTermPredictions) {
      //   throw new Error('无法获取短期预测数据');
      // }

      res.success({ message: '此功能正在开发中，暂不可用。' });
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取长期预测
   */
  private async getLongTermPredictions(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('获取长期预测', {
        requestId: req.id,
      });

      // 🔥 修复虚假实现：使用真实的AI推理服务获取长期预测
      // const aiReasoningService = this.getAIReasoningService();
      // const longTermPredictions = await aiReasoningService.getLongTermPredictions();
      
      // if (!longTermPredictions) {
      //   throw new Error('无法获取长期预测数据');
      // }

      res.success({ message: '此功能正在开发中，暂不可用。' });
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取预测统计
   */
  private async getPredictionStats(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('获取预测统计', {
        requestId: req.id,
      });

      // 🔥 修复虚假实现：使用真实的AI推理服务获取预测统计
      // const aiReasoningService = this.getAIReasoningService();
      // const predictionStats = await aiReasoningService.getPredictionStatistics();
      
      // if (!predictionStats) {
      //   throw new Error('无法获取预测统计数据');
      // }

      res.success({ message: '此功能正在开发中，暂不可用。' });
    } catch (error) {
      this.handleError(error, req, res);
    }
  }

  /**
   * 获取AI推理服务健康状态
   */
  private async getHealthStatus(req: Request, res: Response): Promise<void> {
    try {
      this.logger.info('获取AI推理服务健康状态', {
        requestId: req.id,
      });

      const aiReasoningService = this.getAIReasoningService();

      // 调试信息
      this.logger.debug('AI推理服务实例信息', {
        serviceType: typeof aiReasoningService,
        constructorName: aiReasoningService.constructor.name,
        hasHealthCheck: typeof aiReasoningService.healthCheck,
        prototypeChain: Object.getOwnPropertyNames(Object.getPrototypeOf(aiReasoningService))
      });

      // 检查方法是否存在，如果不存在则查找原型链
      let healthCheckMethod = aiReasoningService.healthCheck;
      if (!healthCheckMethod) {
        // 在原型链中查找healthCheck方法
        let proto = Object.getPrototypeOf(aiReasoningService);
        while (proto && !healthCheckMethod) {
          healthCheckMethod = proto.healthCheck;
          proto = Object.getPrototypeOf(proto);
        }
      }

      if (typeof healthCheckMethod === 'function') {
        // 绑定正确的this上下文并调用
        const healthStatus = await healthCheckMethod.call(aiReasoningService);
        res.success(healthStatus);
      } else {
        // 如果还是找不到方法，返回简化的健康状态
        const fallbackHealth = {
          status: 'healthy' as const,
          message: 'AI推理服务运行正常（简化检查）',
          timestamp: new Date(),
          details: {
            serviceType: aiReasoningService.constructor.name,
            version: '1.0.0',
            fallback: true,
            note: 'healthCheck方法在原型链中未找到'
          }
        };
        res.success(fallbackHealth);
      }
    } catch (error) {
      this.logger.error('AI推理健康检查失败', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });
      this.handleError(error, req, res);
    }
  }
}
