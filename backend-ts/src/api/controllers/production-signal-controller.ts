/**
 * 生产信号控制器
 * 提供生产环境的交易信号生成和管理API
 */

import { Request, Response } from 'express';
import { injectable, inject } from 'inversify';
import { Logger } from 'winston';
import { TYPES } from '../../shared/infrastructure/di/types/index';
import { ProductionSignalService, ProductionSignalRequest } from '../../contexts/trading-signals/application/services/production-signal-service';
import { RealSignalGenerationService } from '../../contexts/trading-signals/application/services/real-signal-generation-service';
import { IUnifiedTechnicalIndicatorCalculator } from '../../shared/infrastructure/technical-indicators/unified-technical-indicator-calculator';
import { UNIFIED_TECHNICAL_INDICATOR_TYPES } from '../../shared/infrastructure/technical-indicators/types';
import { BinanceAdapter } from '../../contexts/market-data/infrastructure/external/binance-adapter';

// 增强信号功能导入（从enhanced-signal-controller.ts合并）
import {
  EnhancedSignalFusionAdapter,
  EnhancedFusedSignalRequest,
  EnhancedFusedTradingSignal
} from '../../contexts/trend-analysis/infrastructure/adapters/signal-fusion-adapter';
import { TradingSymbol } from '../../contexts/market-data/domain/value-objects/trading-symbol';
import { Timeframe } from '../../contexts/market-data/domain/value-objects/timeframe';


export interface SignalRequest {
  symbol: string;
  timeframe?: string;
  analysisDepth?: 'basic' | 'standard' | 'comprehensive';
  includeRiskAssessment?: boolean;
}

// 增强信号请求接口（从enhanced-signal-controller.ts合并）
export interface EnhancedSignalRequest {
  symbol: string;
  timeframe?: string;
  analysisDepth?: 'basic' | 'standard' | 'comprehensive';
  accountInfo?: any;
  collaborativeOptions?: any;
}

export interface SignalResponse {
  signal: 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL';
  confidence: number;
  symbol: string;
  timeframe: string;
  timestamp: Date;
  reasoning: string;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  targetPrice?: number;
  stopLoss?: number;
  metadata: {
    analysisDepth: string;
    dataQuality: number;
    marketCondition: string;
  };
}

@injectable()
export class ProductionSignalController {
  constructor(
    @inject(TYPES.Logger) private readonly logger: Logger,
    @inject(TYPES.TradingAnalysis.ProductionSignalService) private readonly productionSignalService: ProductionSignalService,
    @inject(TYPES.TradingAnalysis.RealSignalGenerationService) private readonly realSignalService: RealSignalGenerationService,
    @inject(UNIFIED_TECHNICAL_INDICATOR_TYPES.UnifiedTechnicalIndicatorCalculator) private readonly technicalCalculator: IUnifiedTechnicalIndicatorCalculator,
    @inject(TYPES.MarketData.BinanceAdapter) private readonly binanceAdapter: BinanceAdapter,
    // 增强信号功能（从enhanced-signal-controller.ts合并）
    @inject(TYPES.TradingAnalysis.EnhancedSignalFusionCoordinator) private readonly enhancedCoordinator: EnhancedSignalFusionAdapter
  ) {}

  /**
   * 生成生产交易信号
   */
  async generateSignal(req: Request, res: Response): Promise<void> {
    try {
      const { symbol, timeframe = '1h', analysisDepth = 'standard', includeRiskAssessment = true } = req.body as SignalRequest;

      if (!symbol) {
        res.status(400).json({
          success: false,
          error: 'Symbol is required'
        });
        return;
      }

      this.logger.info('生成生产交易信号', { symbol, timeframe, analysisDepth });

      // 🔥 使用真实的AI信号生成服务 - 零容忍虚假实现
      const realSignalRequest: ProductionSignalRequest = {
        symbol,
        timeframe,
        analysisDepth: analysisDepth === 'basic' ? 'quick' : analysisDepth as 'quick' | 'standard' | 'comprehensive',
        strategy: 'BALANCED' as 'CONSERVATIVE' | 'BALANCED' | 'AGGRESSIVE',
        enableQualityMonitoring: true,
        enablePerformanceTracking: true
      };

      const realSignal = await this.productionSignalService.generateProductionSignal(realSignalRequest);

      // 转换为控制器响应格式
      const signal: SignalResponse = {
        signal: realSignal.signal as 'BUY' | 'SELL' | 'HOLD' | 'NO_SIGNAL',
        confidence: realSignal.confidence,
        symbol: realSignal.symbol,
        timeframe: realSignal.timeframe,
        timestamp: new Date(realSignal.timestamp),
        reasoning: realSignal.reasoning.join('; '),
        riskLevel: realSignal.riskLevel === 'VERY_HIGH' ? 'HIGH' : realSignal.riskLevel as 'LOW' | 'MEDIUM' | 'HIGH',
        targetPrice: realSignal.targetPrice,
        stopLoss: realSignal.stopLoss,
        metadata: {
          analysisDepth: analysisDepth,
          dataQuality: realSignal.dataQuality.overall,
          marketCondition: this.determineMarketCondition(realSignal)
        }
      };

      res.json({
        success: true,
        data: signal
      });

    } catch (error) {
      this.logger.error('生成生产交易信号失败', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * 获取信号历史记录
   */
  async getSignalHistory(req: Request, res: Response): Promise<void> {
    try {
      const { symbol, limit = 50, offset = 0 } = req.query;

      this.logger.info('获取信号历史记录', { symbol, limit, offset });

      // 🔥 实现真实历史记录获取 - 从数据库查询TradingSignals表
      const whereClause: any = {};
      
      // 如果指定了交易对，添加过滤条件
      if (symbol) {
        // 首先查找symbol的ID
        const symbolRecord = await this.findSymbol(symbol as string);
        
        if (symbolRecord) {
          whereClause.symbolId = symbolRecord.id;
        } else {
          // 如果找不到交易对，返回空结果
          res.json({
            success: true,
            data: {
              signals: [],
              total: 0,
              offset: Number(offset),
              limit: Number(limit)
            }
          });
          return;
        }
      }

      // 查询信号历史记录
      const [signals, total] = await Promise.all([
        this.getSignalHistoryFromDb(whereClause, Number(limit), Number(offset)),
        this.countSignals(whereClause)
      ]);

      // 转换为API响应格式
      const history = signals.map(signal => ({
        id: signal.id,
        signal: signal.signalType,
        strength: signal.strength,
        confidence: Number(signal.confidence),
        symbol: signal.Symbols.symbol,
        timeframe: signal.timeHorizon,
        timestamp: signal.timestamp,
        createdAt: signal.createdAt,
        reasoning: signal.reasoning,
        riskLevel: this.mapRiskLevel(signal.strength * 10), // 基于强度映射风险等级
        targetPrice: signal.takeProfit ? Number(signal.takeProfit) : undefined,
        stopLoss: signal.stopLoss ? Number(signal.stopLoss) : undefined,
        executionStatus: signal.executionStatus,
        validationStatus: signal.validationStatus,
        qualityScore: Number(signal.qualityScore),
        actualReturn: signal.actualReturn ? Number(signal.actualReturn) : undefined,
        performanceScore: signal.performanceScore ? Number(signal.performanceScore) : undefined
      }));

      this.logger.info('信号历史记录查询成功', { 
        symbol, 
        total, 
        returned: history.length,
        offset: Number(offset),
        limit: Number(limit)
      });

      res.json({
        success: true,
        data: {
          signals: history,
          total,
          offset: Number(offset),
          limit: Number(limit)
        }
      });

    } catch (error) {
      this.logger.error('获取信号历史记录失败', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * 获取信号统计信息
   */
  async getSignalStats(req: Request, res: Response): Promise<void> {
    try {
      const { symbol, timeRange = '24h' } = req.query;

      this.logger.info('获取信号统计信息', { symbol, timeRange });

      // 🔥 实现真实统计数据获取 - 从数据库计算TradingSignals表统计
      const whereClause: any = {};
      
      // 计算时间范围
      const now = new Date();
      let startTime: Date;
      
      switch (timeRange) {
        case '1h':
          startTime = new Date(now.getTime() - 60 * 60 * 1000);
          break;
        case '24h':
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
          break;
        case '7d':
          startTime = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case '30d':
          startTime = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          startTime = new Date(now.getTime() - 24 * 60 * 60 * 1000);
      }
      
      whereClause.createdAt = {
        gte: startTime
      };
      
      // 如果指定了交易对，添加过滤条件
      if (symbol) {
        const symbolRecord = await this.findSymbol(symbol as string);
        
        if (symbolRecord) {
          whereClause.symbolId = symbolRecord.id;
        }
      }

      // 查询统计数据
      const [totalSignals, signalsByType, avgConfidence, executedSignals] = await Promise.all([
        // 总信号数
        this.countSignals(whereClause),
        
        // 按类型分组统计
        this.groupSignalsByType(whereClause),
        
        // 平均置信度
        this.calculateAverageConfidence(whereClause),
        
        // 已执行信号的性能统计
        this.getExecutedSignals(whereClause)
      ]);

      // 处理信号类型统计
      const signalCounts = {
        buySignals: 0,
        sellSignals: 0,
        holdSignals: 0
      };
      
      signalsByType.forEach(group => {
        switch (group.signalType.toUpperCase()) {
          case 'BUY':
            signalCounts.buySignals = group._count.signalType;
            break;
          case 'SELL':
            signalCounts.sellSignals = group._count.signalType;
            break;
          case 'HOLD':
            signalCounts.holdSignals = group._count.signalType;
            break;
        }
      });

      // 计算成功率和盈亏
      let successRate = 0;
      let totalReturn = 0;
      let profitableSignals = 0;
      
      if (executedSignals.length > 0) {
        executedSignals.forEach(signal => {
          if (signal.actualReturn) {
            const returnValue = Number(signal.actualReturn);
            totalReturn += returnValue;
            if (returnValue > 0) {
              profitableSignals++;
            }
          }
        });
        
        successRate = (profitableSignals / executedSignals.length) * 100;
      }

      // 计算风险分布
      const riskDistribution = { low: 0, medium: 0, high: 0 };
      executedSignals.forEach(signal => {
        const riskLevel = this.mapRiskLevel(signal.strength * 10);
        switch (riskLevel) {
          case 'LOW':
            riskDistribution.low++;
            break;
          case 'MEDIUM':
            riskDistribution.medium++;
            break;
          case 'HIGH':
            riskDistribution.high++;
            break;
        }
      });

      const stats = {
        totalSignals,
        buySignals: signalCounts.buySignals,
        sellSignals: signalCounts.sellSignals,
        holdSignals: signalCounts.holdSignals,
        averageConfidence: Number(avgConfidence._avg.confidence || 0),
        successRate: Math.round(successRate * 100) / 100,
        profitLoss: { 
          total: Math.round(totalReturn * 10000) / 10000, 
          percentage: Math.round(successRate * 100) / 100 
        },
        riskDistribution,
        timeRange,
        symbol: symbol || 'ALL',
        executedSignalsCount: executedSignals.length,
        period: {
          start: startTime,
          end: now
        }
      };
      
      this.logger.info('信号统计计算完成', { 
        symbol, 
        timeRange, 
        totalSignals, 
        executedSignals: executedSignals.length,
        successRate 
      });

      res.json({
        success: true,
        data: stats
      });

    } catch (error) {
      this.logger.error('获取信号统计信息失败', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * 批量生成信号
   */
  async generateBatchSignals(req: Request, res: Response): Promise<void> {
    try {
      const { symbols, timeframe = '1h', analysisDepth = 'standard' } = req.body;

      if (!symbols || !Array.isArray(symbols) || symbols.length === 0) {
        res.status(400).json({
          success: false,
          error: 'Symbols array is required'
        });
        return;
      }

      this.logger.info('批量生成信号', { symbols, timeframe, analysisDepth });

      // 🔥 使用真实的批量信号生成 - 零容忍虚假实现
      const signals = await Promise.all(symbols.map(async (symbol: string) => {
        try {
          const realSignalRequest: ProductionSignalRequest = {
            symbol,
            timeframe,
            analysisDepth: analysisDepth === 'basic' ? 'quick' : analysisDepth as 'quick' | 'standard' | 'comprehensive',
            strategy: 'BALANCED' as 'CONSERVATIVE' | 'BALANCED' | 'AGGRESSIVE',
            enableQualityMonitoring: true,
            enablePerformanceTracking: true
          };

          const realSignal = await this.productionSignalService.generateProductionSignal(realSignalRequest);

          return {
            signal: realSignal.signal as 'BUY' | 'SELL' | 'HOLD',
            confidence: realSignal.confidence,
            symbol: realSignal.symbol,
            timeframe: realSignal.timeframe,
            timestamp: new Date(realSignal.timestamp),
            reasoning: realSignal.reasoning.join('; '),
            riskLevel: realSignal.riskLevel === 'VERY_HIGH' ? 'HIGH' : realSignal.riskLevel as 'LOW' | 'MEDIUM' | 'HIGH',
            metadata: {
              analysisDepth: analysisDepth,
              dataQuality: realSignal.dataQuality.overall,
              marketCondition: this.determineMarketCondition(realSignal)
            }
          };
        } catch (error) {
          this.logger.error(`批量生成信号失败 - ${symbol}`, { error });
          // 🔥 零容忍虚假数据 - 如果单个信号生成失败，返回基础信号而不是随机数据
          return {
            signal: 'HOLD' as const,
            confidence: 0,
            symbol,
            timeframe,
            timestamp: new Date(),
            reasoning: `信号生成失败 - ${symbol}`,
            riskLevel: 'HIGH' as const,
            metadata: {
              analysisDepth,
              dataQuality: 0,
              marketCondition: 'UNKNOWN'
            }
          };
        }
      }));

      res.json({
        success: true,
        data: {
          signals,
          total: signals.length,
          timestamp: new Date()
        }
      });

    } catch (error) {
      this.logger.error('批量生成信号失败', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * 获取信号配置
   */
  async getSignalConfig(req: Request, res: Response): Promise<void> {
    try {
      const config = {
        supportedSymbols: ['BTCUSDT'],
        supportedTimeframes: ['15m', '1h', '4h', '1d'],
        analysisDepths: ['basic', 'standard', 'comprehensive'],
        riskLevels: ['LOW', 'MEDIUM', 'HIGH'],
        signalTypes: ['BUY', 'SELL', 'HOLD', 'EMPTY'],
        defaultSettings: {
          timeframe: '1h',
          analysisDepth: 'standard',
          includeRiskAssessment: true,
          confidenceThreshold: 0.7
        }
      };

      res.json({
        success: true,
        data: config
      });

    } catch (error) {
      this.logger.error('获取信号配置失败', { error: error instanceof Error ? error.message : String(error) });
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }

  /**
   * 映射风险等级
   */
  private mapRiskLevel(riskScore: number): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (riskScore <= 30) return 'LOW';
    if (riskScore <= 70) return 'MEDIUM';
    return 'HIGH';
  }

  /**
   * 确定市场状况
   */
  private determineMarketCondition(signal: any): string {
    // 基于信号强度和置信度确定市场状况
    if (signal.confidence > 0.8 && signal.strength > 7) {
      if (signal.signal === 'BUY') return 'BULLISH';
      if (signal.signal === 'SELL') return 'BEARISH';
      return 'NEUTRAL';
    }
    if (signal.confidence > 0.6 && signal.strength > 5) {
      if (signal.signal === 'BUY') return 'MODERATELY_BULLISH';
      if (signal.signal === 'SELL') return 'MODERATELY_BEARISH';
      return 'NEUTRAL';
    }
    return 'NEUTRAL';
  }

  // --- 私有数据库访问方法 ---
  private async findSymbol(symbol: string) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.symbols.findFirst({
      where: {
        OR: [
          { symbol: symbol },
          { symbol: symbol.replace('/', '') },
          { symbol: symbol.replace('USDT', '/USDT') }
        ]
      }
    });
  }

  private async getSignalHistoryFromDb(where: any, limit: number, offset: number) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.tradingSignals.findMany({
      where,
      include: {
        Symbols: {
          select: {
            symbol: true,
            baseAsset: true,
            quoteAsset: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    });
  }

  private async countSignals(where: any) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.tradingSignals.count({ where });
  }

  private async groupSignalsByType(where: any) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.tradingSignals.groupBy({
      by: ['signalType'],
      where,
      _count: {
        signalType: true
      }
    });
  }

  private async calculateAverageConfidence(where: any) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.tradingSignals.aggregate({
      where,
      _avg: {
        confidence: true
      }
    });
  }

  private async getExecutedSignals(where: any) {
    // This is a temporary solution. This logic should be moved to a repository.
    const prisma = (this.productionSignalService as any).prisma;
    return await prisma.tradingSignals.findMany({
      where: {
        ...where,
        executionStatus: 'executed',
        actualReturn: {
          not: null
        }
      },
      select: {
        actualReturn: true,
        performanceScore: true,
        strength: true
      }
    });
  }

  /**
   * 生成账户感知的增强交易信号（从enhanced-signal-controller.ts合并）
   * POST /api/v2/signals/enhanced
   */
  public generateEnhancedSignal = async (req: Request, res: Response): Promise<void> => {
    try {
      const {
        symbol,
        timeframe = '1h',
        analysisDepth = 'standard',
        accountInfo,
        collaborativeOptions
      } = req.body as EnhancedSignalRequest;

      // 验证必要参数
      if (!symbol) {
        res.status(400).json({
          success: false,
          error: 'Bad Request',
          message: '交易对符号不能为空'
        });
        return;
      }

      // 构造请求
      const request: EnhancedFusedSignalRequest = {
        symbol: TradingSymbol.create(symbol),
        timeframe: Timeframe.create(timeframe),
        analysisDepth: analysisDepth === 'basic' ? 'quick' : analysisDepth as 'standard' | 'comprehensive',
        accountInfo,
        collaborativeOptions
      };

      this.logger.info('开始生成增强交易信号', {
        symbol,
        timeframe,
        analysisDepth,
        hasAccountInfo: !!accountInfo,
        collaborativeOptions
      });

      // 生成增强信号
      const enhancedSignal = await this.enhancedCoordinator.generateAccountAwareSignal(request);

      // 构造响应
      const response = {
        success: true,
        data: {
          ...enhancedSignal,
          apiVersion: 'v2',
          systemType: 'ENHANCED_FOUR_DIMENSIONAL_FUSION',
          upgradePhase: 'PHASE_1_ACCOUNT_AWARE',
          features: {
            accountAware: !!accountInfo,
            collaborativeAnalysis: !!collaborativeOptions,
            realTimeRiskConstraints: !!collaborativeOptions?.enableRealTimeRisk,
            accountConstraints: !!collaborativeOptions?.enableAccountConstraints
          }
        },
        message: '增强交易信号生成成功'
      };

      this.logger.info('增强交易信号生成完成', {
        symbol,
        signal: enhancedSignal.signal,
        strength: enhancedSignal.strength,
        confidence: enhancedSignal.confidence,
        trendAlignment: enhancedSignal.collaborativeInsights?.trendAlignment,
        riskAssessment: enhancedSignal.collaborativeInsights?.riskAssessment,
        marketConditions: enhancedSignal.collaborativeInsights?.marketConditions
      });

      res.json(response);

    } catch (error) {
      this.logger.error('生成增强交易信号失败', {
        error: error instanceof Error ? error.message : String(error),
        stack: error instanceof Error ? error.stack : undefined
      });

      res.status(500).json({
        success: false,
        error: 'Internal Server Error',
        message: '增强交易信号生成失败'
      });
    }
  };
}
